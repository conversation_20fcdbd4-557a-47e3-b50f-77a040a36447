import base64
import os
import time
import threading
import requests
import subprocess

from concurrent.futures import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>xecutor, as_completed
from typing import List, Dict, Optional, Tuple, Any, Set, Union
from dataclasses import dataclass
from queue import Queue

from github import Github, GithubException, InputGitTreeElement, UnknownObjectException
from github.Repository import Repository
from github.GitCommit import G<PERSON><PERSON><PERSON><PERSON>
from github.PullRequest import PullR<PERSON>quest
from github.GitRef import GitRef
from github.ContentFile import ContentFile
from github.AuthenticatedUser import AuthenticatedUser

from .azure import (
    AzureDevOpsCommit,
    _create_azure_devops_commit_by_repo_id,
    _get_azure_devops_commit_diffs_by_repo_id,
    _get_azure_devops_head_commit_hash_by_repo_id,
    _download_all_git_files_to_disk_azure_devops,
    _download_single_file_azure,
    _get_azure_devops_contents_with_retry,
    _get_azure_devops_credentials_by_repo_id,
    _get_azure_devops_gitmodules_with_retry,
    _get_azure_devops_repo,
    _create_azure_devops_repo,
    _handle_azure_branch_logic,
    AzureRepo,
    create_all_pull_requests_azure_devops,
    create_single_pull_request_azure_devops
)

from .logger import logger
from .consts import GITHUB_CREATE_REPO_DESCRIPTION, SvcType
from .common import (
    get_google_authorized_request_headers,
    blitzy_exponential_retry,
    BlitzyGitFile,
    FailedToFetchCredentials,
)
from .disk import write_file_to_disk, get_cwd
from .service_client import ServiceClient


GitRepo = Union[AzureRepo, Repository]


@blitzy_exponential_retry()
def _get_service_type(user_id: str, git_project_repo_id: str) -> SvcType:
    """Get the service type (GitHub or Azure DevOps) for a repository."""
    try:
        github_handler_server = os.environ.get('SERVICE_URL_GITHUB')

        url = f"{github_handler_server}/v1/users/{user_id}/repositories/{git_project_repo_id}/svc-type"
        logger.info(f"Fetching service type from {url}")
        headers = get_google_authorized_request_headers(url)
        response = requests.get(url, headers=headers, timeout=60)

        if response.status_code == 200:
            data = response.json()
            svc_type_str = data.get('svcType')
            if not svc_type_str:
                raise Exception(f"Service type not found in response for git_project_repo_id {git_project_repo_id}")

            logger.info(f"Service type: {svc_type_str}")
            return SvcType(svc_type_str)
        else:
            err_msg = (
                f"Failed to fetch service type for git_project_repo_id {git_project_repo_id}. "
                f"Status code: {response.status_code}, response: {response.text}"
            )
            raise Exception(err_msg)
    except Exception as e:
        logger.error(f"Error fetching service type: {e}")
        raise


class SimpleContentFile:
    def __init__(self, data):
        self.path: str = data.get("path")
        self.content: str = data.get("content", "")
        self.encoding: str = data.get("encoding", "utf-8")
        self.size: int = data.get("size", 0)
        self.download_url: Optional[str] = data.get("download_url")
        self.sha: Optional[str] = data.get("sha")
        self.type: str = data.get("type", "file")
        self.headers: Optional[Dict[str, str]] = data.get("headers", None)
        self._decoded_content: Optional[bytes] = None

        # Support for _rawData with headers for rate limiting logic
        if self.headers:
            class RawData:
                def __init__(self, headers):
                    self.headers = headers
            self._rawData = RawData(self.headers)
        else:
            self._rawData = None

    @property
    def decoded_content(self):
        if self._decoded_content is not None:
            return self._decoded_content
        if self.encoding == "base64":
            self._decoded_content = base64.b64decode(self.content)
        else:
            self._decoded_content = self.content.encode(self.encoding)
        return self._decoded_content


class GitHubRateLimiter:
    """
    Rate limiter that respects GitHub's rate limit headers.
    Monitors X-RateLimit-Remaining and X-RateLimit-Reset headers.
    """

    def __init__(self, min_remaining: int = 100):
        self.lock = threading.Lock()
        self.min_remaining = min_remaining
        self.rate_limit_reset = 0
        self.rate_limit_remaining = None
        self.request_queue = Queue()

    def update_from_headers(self, headers: Dict[str, str]):
        """Update rate limit info from GitHub response headers."""
        with self.lock:
            if 'X-RateLimit-Remaining' in headers:
                self.rate_limit_remaining = int(headers['X-RateLimit-Remaining'])
            if 'X-RateLimit-Reset' in headers:
                self.rate_limit_reset = int(headers['X-RateLimit-Reset'])

    def wait_if_needed(self):
        """Wait if we're approaching rate limit."""
        with self.lock:
            if self.rate_limit_remaining is not None and self.rate_limit_remaining < self.min_remaining:
                wait_time = self.rate_limit_reset - time.time()
                if wait_time > 0:
                    logger.warning(f"Approaching rate limit. Waiting {wait_time:.1f} seconds...")
                    time.sleep(wait_time + 1)  # Add 1 second buffer


@dataclass
class RepoMetadata:
    """Cached repository metadata to avoid repeated API calls"""
    submodule_paths: Set[str]
    gitmodules_content: Optional[str]

    @classmethod
    def from_repo(cls, repo: GitRepo, ref: str) -> 'RepoMetadata':
        """Create RepoMetadata by fetching from repository once"""
        try:
            gitmodules_text = get_gitmodules_content(repo=repo, ref=ref)
            submodules = parse_gitmodules(gitmodules_text)
            return cls(
                submodule_paths=set(submodules.keys()),
                gitmodules_content=gitmodules_text
            )
        except FileNotFoundError:
            # No .gitmodules file found
            logger.info(f"No .gitmodules file found in repo {repo} at ref {ref}")
            return cls(submodule_paths=set(), gitmodules_content=None)
        except Exception as e:
            logger.error(f"Unexpected error fetching gitmodules: {e}", exc_info=True)
            # Decide: re-raise or return empty metadata
            return cls(submodule_paths=set(), gitmodules_content=None)


@dataclass
class DownloadTask:
    """Represents a file download task."""
    repo_name: str
    user_id: str
    server: str
    repo_id: str
    repo: Repository
    file_path: str
    ref: str
    branch_name: str
    output_path: str
    parent_repo_name: str
    submodule_path: Optional[str] = None
    repo_metadata: Optional[RepoMetadata] = None


@blitzy_exponential_retry()
def get_user_secret_info(user_id: str, server: str):
    """Fetch GitHub access token from the secret server."""
    try:
        url = f"{server}/secret/{user_id}"
        headers = get_google_authorized_request_headers(url)
        response = requests.get(url, headers=headers)

        if response.status_code == 200:
            data = response.json()
            return data.get("accessToken"), data.get("installationID")
        else:
            logger.error(
                f"Failed to fetch access token. Status code: {response.status_code}"
            )
            return None, None
    except Exception as e:
        logger.error(f"Error fetching access token: {e}")
        raise


@blitzy_exponential_retry()
def get_github_installation_secret_info(installation_id: str, server: str):
    """Fetch GitHub access token from the github handler."""
    try:
        url = f"{server}/v1/github/secret/{installation_id}"
        headers = get_google_authorized_request_headers(url)
        response = requests.get(url, headers=headers)

        if response.status_code == 200:
            data = response.json()
            return data.get("accessToken"), data.get("installationID")
        else:
            logger.error(
                f"Failed to fetch access token. Status code: {response.status_code}"
            )
            return None, None
    except Exception as e:
        logger.error(f"Error fetching access token: {e}")
        raise


@blitzy_exponential_retry()
def get_github_installations(access_token: str):
    """Get GitHub installations for the authenticated user."""
    headers = {
        "Accept": "application/vnd.github+json",
        "Authorization": f"Bearer {access_token}",
        "X-GitHub-Api-Version": "2022-11-28",
    }

    response = requests.get(
        "https://api.github.com/user/installations", headers=headers
    )

    if response.status_code == 200:
        data = response.json()
        return data.get("installations")
    else:
        logger.error(
            f"Failed to get installations. Status code: {response.status_code}"
        )
        return None


@blitzy_exponential_retry()
def get_credentials_by_repo_id(repo_id: str) -> Tuple[str, str]:
    """Get GitHub credentials by repo id using github_handler service"""

    github_handler_server = os.environ.get("SERVICE_URL_GITHUB")
    if not github_handler_server:
        raise Exception(
            "SERVICE_URL_GITHUB not set, can't fetch credentials by repo id. Please set it."
        )
    try:
        url = f"{github_handler_server}/v1/github/repositories/{repo_id}/secret/access-token"
        logger.debug(f"Fetching credentials by repo id from {url}")
        headers = get_google_authorized_request_headers(url)
        response = requests.get(url, headers=headers, timeout=60)
        if response.status_code == 200:
            logger.debug("Successfully fetched credentials by repo id")
            data = response.json()
            logger.debug(f"Received response from fetching GitHub credentials by repo id")
            return data['accessToken'], data['installationID']
        else:
            err_msg = (
                f"Failed to fetch credentials from {url} for repo id {repo_id}. "
                f"Status code: {response.status_code}, response: {response.text}"
            )
            raise FailedToFetchCredentials(err_msg)
    except FailedToFetchCredentials:
        raise  # this is an exception we just raised, no need to process just reraise
    except Exception as e:
        logger.error(f"Error fetching access token: {e}")
        raise


def _get_token_and_installation_id(
    server: str, user_id: Optional[str], repo_id: Optional[str]
) -> Tuple[str, str]:
    """
    This function is a temporary wrapper around getting access token and installation ID call. We are currently
    migrating to shared github repository and will use repo_id to get the access token and installation ID instead
    of user_id. But blitzy_utils being used in many places, and we can't make change an atomic operation.
    """
    if repo_id and os.environ.get("SERVICE_URL_GITHUB"):
        logger.info(
            f"Using repo id {repo_id} for getting access token and installation ID."
        )
        access_token, installation_id = get_credentials_by_repo_id(repo_id=repo_id)
    elif user_id and os.environ.get("SERVICE_URL_GITHUB"):
        logger.info(
            f"Using user id {user_id} for getting access token and installation ID."
        )
        access_token, installation_id = get_user_secret_info(
            user_id=user_id, server=server
        )
    else:
        logger.warning("No valid user or repo ID provided.")
        raise FailedToFetchCredentials("No valid user or repo ID provided.")
    if not access_token or not installation_id:
        raise FailedToFetchCredentials("Failed to fetch valid access token or installation ID.")
    return access_token, installation_id


@blitzy_exponential_retry()
def get_github_repo(
        repo_name: str, user_id: str, server: str, create=True, repo_id=None, git_project_repo_id=None
) -> Tuple[GitRepo, bool]:
    """
    Function fetches and returns repo information for github or azure repo. In case of github
    the repo type is github.Repository. In case of azure repo type is .common.AzureRepo

    If the variable git_project_repo_id is set, then we will call archie github handler to determine repo type, otherwise
    we fall back to default behavior and assume repo type is github.

    IMPORTANT!!!!! If the variable `git_project_repo_id` is set, we will ignore all
    other variables for azure and fetch them archie-github-handler. We will keep using these variables for github for now.
    """
    if git_project_repo_id:
        logger.info(
            f"Variable git_project_repo is set, will determine repo type using github-handler service"
        )
        svc_type = _get_service_type(user_id, git_project_repo_id)
        if svc_type == SvcType.GITHUB:
            logger.info(
                f"Repo project {git_project_repo_id} is github repo type, will use github API to get repo info"
            )
            return get_github_repo_github(repo_name, user_id, server, create, repo_id)
        elif svc_type == SvcType.AZURE_DEVOPS:
            logger.info(
                f"Repo project {git_project_repo_id} is azure repo type, will use azure API to get repo info"
            )
            git_project_repo = _get_azure_devops_credentials_by_repo_id(git_project_repo_id)

            logger.info(
                f"Fetched github_project_repo details org_id: {git_project_repo.azure_org_id}, "
                f"azure_project_id: {git_project_repo.azure_project_id}, "
                f"repo_id: {git_project_repo.repo_id}"
            )
            remote_azure_repo = _get_azure_devops_repo(git_project_repo.azure_org_name,
                                                       git_project_repo.azure_project_id,
                                                       git_project_repo.repo_id, git_project_repo.access_token
                                                       )
            if not remote_azure_repo:
                logger.info(
                    f"Didn't find repo {repo_name} in azure devops org {git_project_repo.azure_org_name} project "
                    f"{git_project_repo.azure_project_id}, "
                    f"will create repo"
                )
                new_repo_data = _create_azure_devops_repo(git_project_repo.azure_org_name,
                                                          git_project_repo.azure_project_id, repo_name,
                                                          git_project_repo.access_token)
                repo_id = new_repo_data['id']
                default_branch = new_repo_data['default_branch']
                new_repo = True
            else:
                logger.debug("Azure repo exists, will not create new repo")
                repo_id = remote_azure_repo['id']
                default_branch = remote_azure_repo['default_branch']
                new_repo = False

            azure_repo = AzureRepo(
                name=repo_name,
                org_id=git_project_repo.azure_org_id,
                project_id=git_project_repo.azure_project_id,
                repo_id=repo_id,
                id=repo_id,
                default_branch=default_branch,
            )
            return azure_repo, new_repo
        else:
            raise Exception(f"Unknown service type {svc_type}")

    else:
        logger.info(
            f"Variable git_project_repo_id is not set, assume github repo type")
        return get_github_repo_github(repo_name, user_id, server, create, repo_id)


def get_github_repo_github(repo_name: str, user_id: str, server: str, create=True, repo_id=None) -> Tuple[Repository, bool]:
    """Get or create a GitHub repository using GitHub App authentication.

    Handles both user and organization repositories based on the installation target type.
    """
    # Get user access token
    access_token, installation_id = _get_token_and_installation_id(
        server=server, user_id=user_id, repo_id=repo_id
    )

    if not repo_id:
        # If repo_id is not provided, we assume this is a user repository
        logger.info(f"Fetching repo metadata for user {user_id} and repo {repo_name}")
        raise TypeError("repo_id must be provided for GitHub repository operations.")
    repo_metadata = get_repo_info_by_id(repo_id=repo_id)
    if not access_token:
        raise Exception("Failed to get GitHub access token")

    g = Github(access_token)

    login_name = repo_metadata["orgName"]
    target_type = repo_metadata["installationType"]

    full_repo_name = f"{login_name}/{repo_name}"

    is_new_repo = False

    repo_options = {
        "name": repo_name,
        "private": True,
        "description": GITHUB_CREATE_REPO_DESCRIPTION,
        "has_issues": True,
        "has_projects": True,
        "has_wiki": True,
        "auto_init": True,  # Initialize with README
    }

    try:
        repo = _get_github_repo_github_with_retry(
            g=g,
            full_repo_name=full_repo_name,
        )
    except GithubException as e:
        if e.status == 404 and create:
            repo, is_new_repo = _create_repository_if_not_exists(
                g=g,
                repo_name=repo_name,
                login_name=login_name,
                target_type=target_type,
                repo_options=repo_options,
                create=create
            )
        else:
            logger.error(f"Failed to fetch repo: {full_repo_name}")
            raise

    if repo is None:
        raise Exception(f"Repository {full_repo_name} could not be found or created.")
    return repo, is_new_repo


@blitzy_exponential_retry()
def _create_repository_if_not_exists(
    g: Github,
    repo_name: str,
    login_name: str,
    target_type: str,
    repo_options: dict,
    create: bool
) -> Tuple[Repository, bool]:
    """Create a GitHub repository if it does not exist."""
    logger.warning(f"Repository does not exist: {repo_name}")
    if create:
        logger.info(f"Creating new repository: {repo_name}")
        if target_type == "Organization":
            org = g.get_organization(login_name)
            # Create new organization repository

            repo = org.create_repo(**repo_options)
        else:
            user = g.get_user()
            if isinstance(user, AuthenticatedUser):
                # Create new user repository
                repo = user.create_repo(**repo_options)
        is_new_repo = True
    else:
        raise ValueError(f"Repository '{repo_name}' does not exist and create=False")

    return repo, is_new_repo


@blitzy_exponential_retry()
def _get_github_repo_github_with_retry(g: Github, full_repo_name: str) -> Repository:
    """Get a GitHub repository with retry logic."""
    try:
        return g.get_repo(full_repo_name)
    except GithubException as e:
        if e.status == 404:
            logger.error(f"Repository not found: {full_repo_name}")
            raise
        elif e.status == 403:
            logger.error(f"Access forbidden to repository: {full_repo_name}")
            raise
        elif e.status == 401:
            logger.error(f"Authentication failed for repository: {full_repo_name}")
            raise
        else:
            logger.error(f"GitHub API error accessing repository {full_repo_name}: {e}")
            raise
    except Exception as e:
        logger.error(f"Unexpected error accessing repository {full_repo_name}: {e}")
        raise


def download_single_file(
    repo_name: str,
    user_id: str,
    server: str,
    file_path: str,
    commit_hash: str,
    repo_id: Optional[str] = None,
    git_project_repo_id: Optional[str] = None
) -> Optional[str]:
    """
    Download a single file from the repository at stored commit hash, with submodule support.

    Usage:
        This function is designed to work for both GitHub and Azure DevOps repositories.
        - If `git_project_repo_id` is provided, the function will use the new handler endpoint, which
          supports both GitHub and Azure DevOps.
        - If `git_project_repo_id` is not provided, the function will default to using the legacy GitHub logic.


    Args:
        repo_name: Name of the repository.
        user_id: User ID for authentication.
        server: Server URL for authentication.
        file_path: Path to the file to download.
        commit_hash: Commit hash to fetch the file from.
        repo_id: (Optional) Repository ID, used for legacy GitHub logic.
        git_project_repo_id: (Optional) Project repository ID, required for Azure DevOps and new GitHub handler.

    Returns:
        The file content as a string, or None if not found or on error.
    """
    if not git_project_repo_id:
        svc_type = SvcType.GITHUB
    else:
        svc_type = _get_service_type(user_id, git_project_repo_id)

    repo, _ = get_github_repo(
        repo_name=repo_name,
        user_id=user_id,
        server=server,
        create=False,
        repo_id=repo_id,
        git_project_repo_id=git_project_repo_id
    )

    if svc_type == SvcType.AZURE_DEVOPS and git_project_repo_id:
        logger.info(f"Fetching Azure DevOps repo for download_single_file: "
                    f"repo_name={repo_name}, user_id={user_id}, server={server}, git_project_repo_id={git_project_repo_id}")
        git_project_repo = _get_azure_devops_credentials_by_repo_id(git_project_repo_id)
        content = _download_single_file_azure(
            access_token=git_project_repo.access_token,
            organization=git_project_repo.azure_org_name,
            project_id=git_project_repo.azure_project_id,
            repo_id=git_project_repo.repo_id,
            path=file_path,
            commit_hash=commit_hash,
        )
        content = SimpleContentFile(content) if content else None
        content = decode_file_content(content_file=content) if content else None
    elif svc_type == SvcType.GITHUB:
        if not isinstance(repo, Repository):
            logger.error(f"Expected a GitHub repository, but got {type(repo)}")
            raise TypeError(f"Expected a GitHub repository, but got {type(repo)}")
        content = _download_single_file_github(
            github_repo=repo,
            repo_name=repo_name,
            user_id=user_id,
            server=server,
            file_path=file_path,
            commit_hash=commit_hash,
            repo_id=repo_id,
        )
    else:
        logger.error(f"Unsupported service type: {svc_type}")
        raise ValueError(f"Unsupported service type: {svc_type}")
    if content is None:
        logger.error(f"Failed to download file {file_path} from repo {repo_name} at commit {commit_hash}")
        return None
    logger.info(f"Successfully downloaded file {file_path} from repo {repo_name} at commit {commit_hash}")
    return content


def _download_single_file_github(
    github_repo: Repository,
    repo_name: str,
    user_id: str,
    server: str,
    file_path: str,
    commit_hash: str,
    repo_id: Optional[str] = None,
) -> Optional[str]:
    """
        Download a single file from a GitHub repository at a specific commit.

        This function uses the PyGithub library to fetch file contents from GitHub.
        It is designed to also support Azure DevOps repositories when `repo_id` (project_repo_id) is provided,
        leveraging a new handler endpoint that abstracts away platform differences.

        Args:
            github_repo (Repository): PyGithub Repository object. Can be None if using Azure DevOps via `repo_id`.
            repo_name (str): Name of the repository.
            user_id (str): Identifier of the user requesting the file.
            server (str): Git server URL or identifier.
            file_path (str): Path to the file within the repository.
            commit_hash (str): Specific commit SHA to fetch the file from.
            repo_id (Optional[str]): Optional project repository ID, used to switch handling to a unified endpoint.

        Returns:
            Optional[str]: File content as a string if download succeeds, otherwise None.
        """
    logger.info(f"Fetching GitHub repo for download_single_file: "
                f"repo_name={repo_name}, user_id={user_id}, server={server}, repo_id={repo_id}")

    try:
        file_content = get_contents_with_retry(
            repo=github_repo, path=file_path, ref=commit_hash)
        if isinstance(file_content, list):
            logger.warning(f"Attempted to download folder instead of file: {file_path}")
            return None
        logger.info(f"Downloaded file {file_path} from GitHub into memory")
        return decode_file_content(content_file=file_content)

    except GithubException as e:
        # If file not found (404), check if it's in a submodule
        if e.status == 404:
            logger.info(
                f"File {file_path} not found in main repo, checking submodules..."
            )

            try:
                submodule_repo, relative_path, matching_submodule_path = (
                    get_submodule_for_file(
                        repo=github_repo,
                        user_id=user_id,
                        server=server,
                        file_path=file_path,
                        head_commit_hash=commit_hash,
                    )
                )

                if not submodule_repo:
                    return None

                # Get the submodule commit hash at this specific commit in the main repo
                try:
                    # Ensure matching_submodule_path is not None
                    if matching_submodule_path is None:
                        logger.error("matching_submodule_path is None, cannot fetch submodule commit entry")
                        return None

                    # Get the commit object for the submodule at this specific commit in parent repo
                    submodule_commit_entry = get_contents_with_retry(
                        repo=github_repo, path=matching_submodule_path, ref=commit_hash
                    )
                    if isinstance(submodule_commit_entry, list):
                        if len(submodule_commit_entry) == 1:
                            submodule_commit_entry = submodule_commit_entry[0]
                        else:
                            logger.warning(
                                f"Expected a single submodule commit entry for {matching_submodule_path}, got {len(submodule_commit_entry)} items"
                            )
                            submodule_commit_entry = submodule_commit_entry[0]
                    submodule_commit_hash = submodule_commit_entry.sha

                    # Try to get the file content using the submodule's commit hash
                    if relative_path is not None and submodule_commit_hash is not None:
                        submodule_file = get_contents_with_retry(
                            repo=submodule_repo,
                            path=relative_path,
                            ref=submodule_commit_hash,
                        )
                        logger.info(
                            f"Downloaded file {relative_path} at commit {submodule_commit_hash}"
                        )
                    else:
                        logger.error(
                            f"Cannot fetch file from submodule: relative_path or submodule_commit_hash is None (relative_path={relative_path}, submodule_commit_hash={submodule_commit_hash})"
                        )
                        return None
                    # ensure only one file returned
                    if isinstance(submodule_file, list):
                        if len(submodule_file) == 1:
                            submodule_file = submodule_file[0]
                        else:
                            logger.warning(
                                f"Expected a single file for {relative_path} in submodule, got {len(submodule_file)} items"
                            )
                            submodule_file = submodule_file[0]
                    return decode_file_content(content_file=submodule_file)
                except GithubException as commit_error:
                    logger.warning(f"Could not get file from submodule at specific commit: {commit_error}")
                    return None

            except Exception as submodule_error:
                logger.error(f"Error processing submodules: {submodule_error}")
                return None
        else:
            logger.error(f"Error downloading {file_path} at commit {commit_hash}: {e}")
            return None


@blitzy_exponential_retry()
def decode_file_content(content_file: ContentFile | SimpleContentFile) -> Optional[str]:
    """
    Safely decode file content from GitHub API.
    Returns None for submodule references.
    """

    try:
        # Check if this is a large file (encoding is None)
        if content_file.content == "" or content_file.encoding == "none":
            # Large file - use download_url
            logger.info(
                f"Large file detected ({content_file.size} bytes), downloading from URL"
            )
            if not content_file.download_url:
                logger.error(f"No download URL available for file {content_file.path}")
                return None
            response = requests.get(content_file.download_url)
            response.raise_for_status()

            # Try to decode as text
            try:
                return response.text
            except UnicodeDecodeError:
                # Try other encodings
                encodings = ["latin-1", "cp1252", "utf-16", "ascii"]
                for encoding in encodings:
                    try:
                        return response.content.decode(encoding)
                    except UnicodeDecodeError:
                        continue

                logger.error(
                    f"Could not decode file {content_file.path}, all encodings failed"
                )
                return None
        else:
            # Regular file - use decoded_content (PyGithub handles the encoding)
            decoded_bytes = content_file.decoded_content

            # Try UTF-8 decoding first
            try:
                return decoded_bytes.decode("utf-8")
            except UnicodeDecodeError:
                # Try other common encodings
                encodings = ["latin-1", "cp1252", "utf-16", "ascii"]
                for encoding in encodings:
                    try:
                        return decoded_bytes.decode(encoding)
                    except UnicodeDecodeError:
                        continue

                # If all text decodings fail, return as binary string representation
                # or handle binary files differently
                logger.error(
                    f"Could not decode file {content_file.path}, all encodings failed"
                )
                return None  # Return raw bytes for binary files

    except Exception as e:
        logger.error(f'Error decoding {content_file.path}: {e}')
        raise


def parse_gitmodules(content: str | None) -> dict:
    """Parse .gitmodules content and return a dictionary mapping paths to repository information."""
    result = {}
    if not content:
        return result
    submodules = {}
    current_submodule = None

    lines = content.strip().split("\n")
    for line in lines:
        line = line.strip()
        if not line or line.startswith("#"):
            continue

        if line.startswith('[submodule "'):
            # Extract submodule name between quotes
            name = line[line.find('"') + 1: line.rfind('"')]
            current_submodule = name
            submodules[current_submodule] = {"name": current_submodule}
        elif current_submodule and "=" in line:
            key, value = [part.strip() for part in line.split("=", 1)]
            submodules[current_submodule][key] = value

    # Reorganize to use path as the key
    for name, info in submodules.items():
        if "path" in info and "url" in info:
            result[info["path"]] = {"name": name, "url": info["url"]}

    return result


def ssh_to_https_url(ssh_url: str) -> str:
    """Convert a Git SSH URL to HTTPS URL, supporting both GitHub.com and GitHub Enterprise."""
    # Handle standard GitHub SSH URL (**************:owner/repo.git)
    if ssh_url.startswith("**************:"):
        path = ssh_url[15:]  # Remove '**************:'
        return f"https://github.com/{path}"

    # Handle GitHub Enterprise SSH URL (************************:owner/repo.git)
    elif ssh_url.startswith("git@") and ":" in ssh_url:
        # Extract the domain and path
        domain_part = ssh_url[
            4: ssh_url.find(":")
        ]  # Extract domain part (github.mycompany.com)
        path = ssh_url[ssh_url.find(":") + 1:]  # Extract path part (owner/repo.git)
        return f"https://{domain_part}/{path}"

    # Handle SSH protocol URLs (ssh://**************/owner/repo.git)
    elif ssh_url.startswith("ssh://git@"):
        # Remove 'ssh://git@' prefix
        server_path = ssh_url[10:]
        # Split into server and path parts
        server, *path_parts = server_path.split("/", 1)
        path = path_parts[0] if path_parts else ""
        return f"https://{server}/{path}"

    # If it's already an HTTPS URL or another format, return as is
    return ssh_url


def extract_repo_info_from_url(url: str) -> Tuple[str, str, str]:
    """Extract owner, repo name, and domain from a GitHub URL."""
    # Convert to HTTPS if it's SSH
    https_url = ssh_to_https_url(url)

    # Check if it's an HTTPS URL
    if https_url.startswith("https://"):
        # Extract the domain and path
        url_parts = https_url[8:].split("/", 1)
        if len(url_parts) < 2:
            return "", "", url_parts[0] if url_parts else ""

        domain = url_parts[0]  # e.g., github.com or github.mycompany.com
        path = url_parts[1]  # e.g., owner/repo.git

        # Remove .git extension if present
        if path.endswith(".git"):
            path = path[:-4]

        # Split by / to get owner and repo
        parts = path.split("/")
        if len(parts) >= 2:
            return parts[0], parts[1], domain

    # Return empty strings if parsing fails
    return "", "", ""


@blitzy_exponential_retry()
def get_all_github_file_paths(
    repo_name: str,
    user_id: str,
    server: str,
    commit_hash: str,
    repo_id: str = None,
    git_project_repo_id: Optional[str] = None,
) -> List[str]:
    # deprecated - see download_all_git_files_to_disk
    """
    Get all file paths from a GitHub repository, optionally including files in submodules.

    Usage:
        This function is designed to work for both GitHub and Azure DevOps repositories.
        - If `project_repo_id` is provided, the function will use the new handler endpoint, which
          supports both GitHub and Azure DevOps.
        - If `project_repo_id` is not provided, the function will default to using the legacy GitHub logic.

    Args:
        repo_name: Name of the repository
        user_id: User ID for authentication
        server: Server URL for authentication
        commit_hash: Commit hash to get files from
        repo_id: ID of the repository, if available(will be used to fetch credentials)
        git_project_repo_id: (Optional) Project repository ID, required for Azure DevOps and new GitHub handler.

    Returns:
        List of file paths
    """
    all_files = []

    def get_contents(
        repo_name: str,
        user_id: str,
        server: str,
        repo_id: str,
        repo: GitRepo,
        path: str,
        commit_hash: str,
        git_project_repo_id: Optional[str] = None
    ) -> List[str]:
        contents = []
        try:
            items = get_repo_contents_with_retry(
                repo_name=repo_name,
                user_id=user_id,
                server=server,
                repo_id=repo_id,
                repo=repo,
                path=path,
                ref=commit_hash,
                git_project_repo_id=git_project_repo_id
            )

            # Handle single file
            if not isinstance(items, list):
                items = [items]

            for item in items:
                if item.type == "dir":
                    contents.extend(get_contents(
                        repo_name=repo_name,
                        user_id=user_id,
                        server=server,
                        repo_id=repo_id,
                        repo=repo,
                        path=item.path,
                        commit_hash=commit_hash,
                        git_project_repo_id=git_project_repo_id
                    ))
                elif item.type == "file":
                    contents.append(item.path)
        except GithubException as e:
            logger.error(f"Error accessing {path}: {e}")
            return []

        return contents

    github_repo, _ = get_github_repo(repo_name=repo_name, user_id=user_id,
                                     server=server, create=False, repo_id=repo_id, git_project_repo_id=git_project_repo_id)
    # Get files in the main repository

    main_files = get_contents(
        repo_name=repo_name,
        user_id=user_id,
        server=server,
        repo_id=repo_id,
        repo=github_repo,
        path="",
        commit_hash=commit_hash,
        git_project_repo_id=git_project_repo_id
    )
    all_files.extend(main_files)

    try:
        gitmodules_text = get_gitmodules_content(
            repo=github_repo, ref=commit_hash, git_project_repo_id=git_project_repo_id)
        submodules = parse_gitmodules(gitmodules_text)

        # Process each submodule
        for submodule_path, info in submodules.items():
            github_repo, _ = get_github_repo(repo_name=repo_name, user_id=user_id,
                                             server=server, create=False, repo_id=repo_id, git_project_repo_id=git_project_repo_id)

            # Get submodule commit hash
            submodule_commit = get_submodule_commit_sha(
                repo=github_repo, submodule_path=submodule_path, commit_hash=commit_hash)
            if not submodule_commit:
                logger.warning(
                    f"Couldn't determine commit for submodule {submodule_path}"
                )
                continue

            # Get the submodule repo
            submodule_url = info["url"]
            # TODO: Support other VCS providers
            if not submodule_url:
                logger.warning(
                    f"No URL found for submodule {submodule_path}, skipping..."
                )
                continue
            https_url = ssh_to_https_url(submodule_url)
            submodule_owner, submodule_repo_name, domain = extract_repo_info_from_url(
                https_url
            )

            if not submodule_owner or not submodule_repo_name:
                logger.error(f"Could not extract owner/repo from URL: {https_url}")
                continue

            # Get access token and create GitHub client
            access_token, _ = _get_token_and_installation_id(server, user_id, repo_id)

            if domain and domain != "github.com":
                g = Github(
                    base_url=f"https://{domain}/api/v3", login_or_token=access_token
                )
            else:
                g = Github(access_token)

            try:
                submodule_repo = g.get_repo(f"{submodule_owner}/{submodule_repo_name}")

                # Get all files in the submodule
                submodule_files = get_contents(
                    repo_name=submodule_repo_name,
                    user_id=user_id,
                    server=server,
                    repo_id=str(submodule_repo.id),
                    repo=submodule_repo,
                    path="",
                    commit_hash=submodule_commit,
                    git_project_repo_id=git_project_repo_id
                )

                # Add submodule path prefix to all files
                for file_path in submodule_files:
                    all_files.append(f"{submodule_path}/{file_path}")

                logger.info(
                    f"Added {len(submodule_files)} files from submodule {submodule_path}"
                )

            except GithubException as e:
                logger.error(f"Error accessing submodule repo: {e}")
                raise

    except GithubException as e:
        logger.warning(f"No .gitmodules file found: {e}")

    return all_files


def get_contents_with_retry(
    repo: GitRepo,
    path: str,
    ref: str,
    git_project_repo_id: Optional[str] = None,
) -> Union[ContentFile, SimpleContentFile, List[ContentFile], List[SimpleContentFile]]:
    """
    Wrapper function to get contents with retry logic. Does not expand into submodules

    Usage:
        This function is designed to work for both GitHub and Azure DevOps repositories.
        - If `project_repo_id` is provided, the function will use the new handler endpoint, which
          supports both GitHub and Azure DevOps.

    Args:
        repo: GitHub repository object (can be None if project_repo_id is provided)
        path: Path to the file or directory
        ref: Git reference (branch, tag, or commit hash)
        git_project_repo_id: (Optional) Project repository ID, required for Azure DevOps and new GitHub handler.

    Returns:
        List of or singular GitHub ContentFile object and SimpleContentFile util object
    """
    if isinstance(repo, Repository):  # if repo is a GitHub repository
        # Use PyGithub to get contents from the repository
        result = _get_contents_with_retry_github(
            repo=repo, path=path, ref=ref
        )
        content = result if result else None
    elif isinstance(repo, AzureRepo) and git_project_repo_id:  # if git_project_repo_id is provided, use the new endpoint
        # Compose the endpoint URL
        result = _get_azure_devops_contents_with_retry(git_project_repo_id=git_project_repo_id, path=path, ref=ref)

        content = SimpleContentFile(result.serialize()) if result else None
    else:
        raise ValueError("Repository or Project Repo ID is required to fetch contents")
    if not content:
        raise UnknownObjectException(404, f"File not found: {path}")
    # If we want to ensure we only get a single file, we can uncomment the following lines
    # but this will raise an error if multiple files are returned, which may not be desired
    # if isinstance(content, list):
    #     logger.info(f"Fetched {len(content)} items from {path} at ref {ref}")
    #     raise ValueError(
    #         f"Expected a single file but got {len(content)} items for path '{path}' at ref '{ref}'"
    #     )
    return content


@blitzy_exponential_retry()
def _get_contents_with_retry_github(
    repo: Repository,
    path: str,
    ref: str,
) -> Union[ContentFile, List[ContentFile]]:
    try:
        logger.info(f"Fetching contents from GitHub for path '{path}' at ref '{ref}'")
        return repo.get_contents(path, ref=ref)
    except GithubException as e:
        if e.status == 404:
            logger.warning(f"File not found in repo: {path} at ref {ref}")
            raise UnknownObjectException(404, f"File not found: {path}")
        else:
            logger.error(f"Error fetching contents from repo: {e}")
            raise e


def process_single_file(
    repo: GitRepo,
    item: ContentFile | SimpleContentFile,
    repo_name: str,
    branch_name: str,
    commit_hash: str,
    git_project_repo_id: Optional[str] = None,
) -> Optional[BlitzyGitFile]:
    """
    Process a single file.

    Usage:
        This function is designed to work for both GitHub and Azure DevOps repositories.
        - If `git_project_repo_id` is not provided, the function will default to using the legacy GitHub logic.

    Args:
        repo: GitRepo object (can be None if git_project_repo_id is provided)
        item: File content item to process
        repo_name: Name of the repository
        branch_name: Branch name
        commit_hash: Commit hash
        git_project_repo_id: (Optional) Project repository ID.

    Returns:
        BlitzyGitFile object if successful, None if file should be skipped

    """
    try:
        # For files, we might need to fetch the content separately
        # if decoded_content is not available
        if hasattr(item, "decoded_content") and item.decoded_content is not None:
            file_text = decode_file_content(content_file=item)
        else:
            # Fetch file content - this will retry on connection errors
            logger.info(f"Fetching content for {item.path}")
            file_content = get_contents_with_retry(
                repo=repo, path=item.path, ref=commit_hash, git_project_repo_id=git_project_repo_id
            )
            # Ensure we only decode a single file, not a list
            if isinstance(file_content, list):
                if len(file_content) == 1:
                    file_content = file_content[0]
                else:
                    logger.warning(
                        f"Expected a single file for {item.path}, got {len(file_content)} items"
                    )
                    file_content = file_content[0]
            file_text = decode_file_content(content_file=file_content)

        # Create the file object
        if file_text is None:
            logger.warning(f"File content for {item.path} is None, skipping file.")
            return None
        blitzy_file = BlitzyGitFile(path=item.path, text=file_text)

        write_file_to_disk(
            file_path=item.path,
            file_text=file_text,
            repo_name=repo_name,
            branch_name=branch_name,
        )

        return blitzy_file
    except AssertionError as e:
        logger.warning(
            f"Skipping file due to encoding error {item.path}: {type(e).__name__}: {str(e)}"
        )
        return None
    except Exception as e:
        # This will be caught by the retry decorator first
        # Only if all retries fail will it bubble up
        logger.warning(
            f"Error processing file {item.path}: {type(e).__name__}: {str(e)}"
        )
        raise


@blitzy_exponential_retry()
def get_gitmodules_content(
    repo: GitRepo,
    ref: str,
    git_project_repo_id: Optional[str] = None,
) -> Optional[str]:
    """
    Get .gitmodules content from a repository.

    Usage:
        This function is designed to work for both GitHub and Azure DevOps repositories.
        - If `git_project_repo_id` is provided, the function will use the new handler endpoint, which
          supports both GitHub and Azure DevOps.
        - If `git_project_repo_id` is not provided, the function will default to using the legacy GitHub logic.

    Args:
        repo: GitHub repository object (can be None if git_project_repo_id is provided)
        ref: Git reference (branch, tag, or commit hash)
        git_project_repo_id: (Optional) Project repository ID, required for Azure DevOps and new GitHub handler.

    Returns:
        Content of .gitmodules file as string, or None if not found
    """
    if isinstance(repo, Repository):
        try:
            gitmodules_content = repo.get_contents(".gitmodules", ref=ref)
            if isinstance(gitmodules_content, list):
                if len(gitmodules_content) == 1:
                    gitmodules_content = gitmodules_content[0]
                else:
                    logger.warning(
                        f"Expected a single .gitmodules file, got {len(gitmodules_content)} items"
                    )
                    gitmodules_content = gitmodules_content[0]
            return decode_file_content(content_file=gitmodules_content)
        except GithubException as e:
            if e.status == 404:
                raise UnknownObjectException(404, "File not found: .gitmodules")
            else:
                raise e

    elif isinstance(repo, AzureRepo) and git_project_repo_id:  # if git_project_repo_id is provided, use the new endpoint
        result = _get_azure_devops_gitmodules_with_retry(
            git_project_repo_id=git_project_repo_id,
            ref=ref
        )

        if not result:
            raise ValueError("Couldn't get contents from AzureRepo")

        if hasattr(result, "serialize"):
            data = result.serialize()
        else:
            raise TypeError("Expected 'result' to have a 'serialize' method")

        content = SimpleContentFile(data)
        if content is not None:
            return decode_file_content(content_file=content)
        else:
            raise Exception("File not found: .gitmodules")
    else:
        raise ValueError("Repository or project_repo_id is required to fetch .gitmodules using PyGithub")


@blitzy_exponential_retry()
def get_submodule_repo(github_client: Github, owner: str, repo_name: str):
    """Get submodule repository with retry logic"""
    return github_client.get_repo(f"{owner}/{repo_name}")


@blitzy_exponential_retry()
def get_submodule_commit_sha_with_retry(
    repo: Optional[Repository], submodule_path: str, commit_hash: str, git_project_repo_id: Optional[str] = None
) -> Optional[str]:
    """
    Get the commit SHA of a submodule at a specific commit.

    Usage:
        This function is designed to work for both GitHub and Azure DevOps repositories.
        - If `git_project_repo_id` is provided, the function will use the new handler endpoint, which
          supports both GitHub and Azure DevOps.
        - If `git_project_repo_id` is not provided, the function will default to using the legacy GitHub logic.

    Args:
        repo: GitHub repository object (can be None if git_project_repo_id is provided)
        submodule_path: Path to the submodule
        commit_hash: Commit hash to get submodule info from
        git_project_repo_id: (Optional) Project repository ID, required for Azure DevOps and new GitHub handler.

    Returns:
        Commit SHA of the submodule, or None if not found
    """

    if git_project_repo_id:
        logger.error("Fetching submodule commit SHA from the new endpoint (git_project_repo_id) is not implemented")
        return None

    if not repo:
        logger.error("Repository object is required to fetch submodule commit SHA when git_project_repo_id is not provided")
        return None

    commit_obj = repo.get_git_commit(commit_hash)
    tree = repo.get_git_tree(commit_obj.tree.sha, recursive=True)

    for item in tree.tree:
        if item.path == submodule_path and item.type == "commit":
            logger.info(f"Found submodule {submodule_path} with SHA {item.sha}")
            return item.sha

    return None


def is_submodule_reference(content_file: ContentFile | SimpleContentFile, submodule_paths: Set[str]) -> bool:
    """
    Check if a ContentFile object represents a submodule reference.
    Now accepts pre-fetched submodule_paths to avoid repeated API calls.
    """
    # Check if it's explicitly marked as submodule
    if content_file.path in submodule_paths or (hasattr(content_file, 'type') and content_file.type == 'submodule'):
        return True

    return False


def download_file_with_rate_limit(
    task: DownloadTask,
    rate_limiter: GitHubRateLimiter
) -> Optional[BlitzyGitFile]:
    """
    Download a single file with rate limiting.
    Returns None if the file is a submodule reference.
    """
    # Wait if approaching rate limit
    rate_limiter.wait_if_needed()

    # Get file content
    file_content = get_contents_with_retry(
        repo=task.repo,
        path=task.file_path,
        ref=task.ref
    )

    # Use cached submodule paths instead of fetching every time
    submodule_paths = task.repo_metadata.submodule_paths if task.repo_metadata else set()

    if isinstance(file_content, list):
        # If we get a list, it means we tried to get a directory instead of a file
        logger.warning(f"Expected a single file, got multiple items for {task.file_path}")
        file_content = file_content[0]
    # Check if it's a submodule reference using cached data
    if is_submodule_reference(content_file=file_content, submodule_paths=submodule_paths):
        logger.info(f"Skipping submodule reference during download: {task.file_path}")
        return None

    # Update rate limiter from response headers if available
    # TODO: Remove access to protected member of _rawData class
    if hasattr(file_content, '_rawData') and file_content._rawData is not None and hasattr(file_content._rawData, 'headers'):
        logger.debug(f"Adding headers from ContentFile {file_content._rawData.header}")
        rate_limiter.update_from_headers(file_content._rawData.headers)
    elif isinstance(file_content, SimpleContentFile):
        logger.debug(f"Adding headers from SimpleFileContent {file_content.headers}")
        rate_limiter.update_from_headers(file_content.headers)

    # Decode content
    content = decode_file_content(content_file=file_content)

    # Skip if content is None (e.g., submodule or decode failure)
    if content is None:
        logger.warning(f"Skipping file {task.file_path} - no decodable content")
        return None

    # Adjust path if it's from a submodule
    path = task.output_path
    if task.submodule_path:
        path = f"{task.submodule_path}/{path}"

    # Create BlitzyGitFile object
    git_file = BlitzyGitFile(
        path=path,
        text=content
    )

    # Write to disk
    write_file_to_disk(
        file_path=git_file.path,
        file_text=git_file.text,
        repo_name=task.parent_repo_name,
        branch_name=task.branch_name
    )

    logger.info(f"Successfully downloaded: {git_file.path}")

    return git_file


def get_submodule_paths(repo: Repository, ref: str) -> set:
    """Get all known submodule paths from .gitmodules"""
    try:
        gitmodules_text = get_gitmodules_content(repo=repo, ref=ref)
        submodules = parse_gitmodules(gitmodules_text)
        return set(submodules.keys())
    except:
        return set()


def get_repo_contents_with_retry(
    repo_name: str,
    user_id: str,
    server: str,
    repo_id: str,
    repo: Optional[GitRepo],
    ref: str,
    path: str = "",
    git_project_repo_id: Optional[str] = None
) -> ContentFile | List[ContentFile] | List[SimpleContentFile] | SimpleContentFile:
    """
    Get repository contents with retry logic.
    Returns a list of (path, type) tuples where type can be 'file', 'dir', or 'submodule'.
    """
    if not repo:
        logger.info(
            f"No repository object provided, fetching repo for repo {repo_name}"
        )
        repo, _ = get_github_repo(repo_name=repo_name,
                                  user_id=user_id,
                                  server=server,
                                  repo_id=repo_id,
                                  git_project_repo_id=git_project_repo_id)
    try:
        return get_contents_with_retry(
            repo=repo,
            path=path,
            ref=ref,
            git_project_repo_id=git_project_repo_id
        )
    except UnknownObjectException as e:
        logger.error(f"File not found in {repo_name} at {path}: {e}")
        raise
    except Exception as e:
        logger.error(f"Error getting contents for {repo_name} at {path}: {e}")
        raise


def get_all_files_in_repo(
    repo_name: str,
    user_id: str,
    server: str,
    repo_id: str,
    repo: GitRepo,
    ref: str,
    path: str = "",
    submodule_paths: Set[str] = None,
    git_project_repo_id: Optional[str] = None
) -> List[Tuple[str, str]]:
    """
    Recursively get all file paths and their types in a repository.
    Returns list of (path, type) tuples where type can be 'file', 'dir', or 'submodule'.
    """
    files = []

    try:
        contents = get_repo_contents_with_retry(
            repo_name=repo_name,
            user_id=user_id,
            server=server,
            repo_id=repo_id,
            repo=repo,
            path=path,
            ref=ref
        )

        if not isinstance(contents, list):
            contents = [contents]

        for content in contents:
            # Check if this path is a known submodule
            if submodule_paths and content.path in submodule_paths:
                files.append((content.path, "submodule"))
                logger.info(f"Found submodule: {content.path}")
            elif content.type == "file":
                files.append((content.path, "file"))
            elif content.type == "dir":
                # Recursively get files in subdirectory
                subfiles = get_all_files_in_repo(repo_name=repo_name,
                                                 user_id=user_id,
                                                 server=server,
                                                 repo_id=repo_id,
                                                 repo=repo,
                                                 ref=ref,
                                                 path=content.path,
                                                 submodule_paths=submodule_paths,
                                                 git_project_repo_id=git_project_repo_id)
                files.extend(subfiles)

    except Exception as e:
        logger.error(f"Error listing files in {path}: {e}")
        raise

    return files


def download_all_git_files_to_disk(
    repo_name: str,
    branch_name: str,
    user_id: str,
    server: str,
    commit_hash: str,
    repo_id: Optional[str] = None,
    max_workers: int = 10,
    rate_limit_threshold: int = 100,
    git_project_repo_id: Optional[str] = None,
) -> List[BlitzyGitFile]:
    """
    Download all files from a git repository including submodules.

    Usage:
        This function is designed to work for both GitHub and Azure DevOps repositories.
        - If `git_project_repo_id` is provided, the function will determine the repository type
          automatically and use the appropriate handler.
        - If `git_project_repo_id` is not provided, the function will default to using the legacy GitHub logic.

    Args:
        repo_name: Name of the repository
        branch_name: Branch name to download from
        user_id: User ID for authentication
        server: Server URL for authentication
        commit_hash: Commit hash to download from
        repo_id: (Optional) Repository ID, used for legacy GitHub logic
        max_workers: Maximum number of concurrent workers for file downloads
        rate_limit_threshold: Rate limit threshold for GitHub API
        git_project_repo_id: (Optional) Project repository ID, required for Azure DevOps and new GitHub handler

    Returns:
        List of BlitzyGitFile objects representing downloaded files

    Note:
        Network errors will be retried at the file level, not the entire operation.
        If any file fails after all retries, the entire operation fails.
    """
    # Get the service type for this repository
    logger.info(f"Downloading all files from {repo_name}/{branch_name} "
                f"with commit hash {commit_hash} where user_id is {user_id} and repo_id is {repo_id}")
    if not git_project_repo_id:
        logger.info("Git project Repo ID not provided, using default gihub service type")
        svc_type = SvcType.GITHUB
    else:
        logger.info(f"Git project Repo ID {git_project_repo_id}, will determine repo type")
        svc_type = _get_service_type(user_id=user_id, git_project_repo_id=git_project_repo_id)
        logger.info(f"Processing repository {repo_name} with service type: {svc_type.value}")

    if svc_type == SvcType.GITHUB:
        return _download_all_git_files_to_disk_github(
            repo_name=repo_name,
            branch_name=branch_name,
            user_id=user_id,
            server=server,
            commit_hash=commit_hash,
            repo_id=repo_id,
            max_workers=max_workers,
            rate_limit_threshold=rate_limit_threshold
        )
    elif svc_type == SvcType.AZURE_DEVOPS:
        return _download_all_git_files_to_disk_azure_devops(
            repo_name=repo_name,
            branch_name=branch_name,
            commit_hash=commit_hash,
            git_project_repo_id=git_project_repo_id
        )
    else:
        raise ValueError(f"Unsupported service type: {svc_type}")


def _download_all_git_files_to_disk_github(
    repo_name: str,
    branch_name: str,
    user_id: str,
    server: str,
    commit_hash: str,
    repo_id: Optional[str] = None,
    max_workers: int = 10,
    rate_limit_threshold: int = 100
) -> List[BlitzyGitFile]:
    """
    Download all files from a GitHub repository including submodules.
    Network errors will be retried at the file level, not the entire operation.
    If any file fails after all retries, the entire operation fails.
    """
    try:
        github_repo, _ = get_github_repo(
            repo_name=repo_name, user_id=user_id, server=server, create=False, repo_id=repo_id
        )
    except Exception as e:
        logger.error(f"Failed to get repository {repo_name}: {e}")
        raise

    all_files = []
    rate_limiter = GitHubRateLimiter(min_remaining=rate_limit_threshold)

    # Fetch repository metadata ONCE
    logger.info("Fetching repository metadata...")
    repo_metadata = RepoMetadata.from_repo(github_repo, commit_hash)

    # Create thread pool for parallel downloads
    with ThreadPoolExecutor(max_workers=max_workers) as executor:
        futures = []

        # Get all files in main repository
        logger.info("Listing files in main repository...")
        try:
            # Pass cached submodule paths
            main_files_list = get_all_files_in_repo(
                repo_name=repo_name,
                user_id=user_id,
                server=server,
                repo_id=repo_id,
                repo=github_repo,
                ref=commit_hash,
                submodule_paths=repo_metadata.submodule_paths
            )
            logger.info(f"Found {len(main_files_list)} files in main repository")

            # Submit download tasks for main repository files (skip submodules)
            for file_path, file_type in main_files_list:
                if file_type == "submodule":
                    logger.info(f"Skipping submodule reference: {file_path}")
                    continue
                if not isinstance(github_repo, Repository):
                    raise ValueError("Expected a PyGithub Repository object")
                if not repo_id:
                    raise ValueError("repo_id is required for download tasks")
                task = DownloadTask(
                    repo_name=repo_name,
                    user_id=user_id,
                    server=server,
                    repo_id=repo_id,
                    repo=github_repo,
                    file_path=file_path,
                    ref=commit_hash,
                    output_path=file_path,
                    branch_name=branch_name,
                    repo_metadata=repo_metadata,  # Pass cached metadata
                    parent_repo_name=github_repo.name
                )
                future = executor.submit(download_file_with_rate_limit, task, rate_limiter)
                futures.append(future)

        except Exception as e:
            logger.error(f"Failed to list main repository files: {e}")
            raise

        # Process submodules using cached gitmodules content
        if repo_metadata.gitmodules_content:
            try:
                submodules = parse_gitmodules(repo_metadata.gitmodules_content)

                # Process each submodule in parallel
                submodule_futures = []

                for submodule_path, info in submodules.items():
                    # Submit submodule processing as a separate task
                    if not isinstance(github_repo, Repository):
                        raise ValueError("Expected a PyGithub Repository object")
                    logger.info(f"Processing submodule: {submodule_path}")
                    future = executor.submit(
                        process_submodule_parallel,
                        github_repo, submodule_path, info, commit_hash, branch_name,
                        user_id, server, repo_id, rate_limiter, executor
                    )
                    submodule_futures.append((submodule_path, future))

                # Collect submodule results
                for submodule_path, future in submodule_futures:
                    try:
                        submodule_tasks = future.result()
                        futures.extend(submodule_tasks)
                        logger.info(f"Queued {len(submodule_tasks)} files from submodule {submodule_path}")
                    except Exception as e:
                        logger.error(f"Failed to process submodule {submodule_path}: {e}")
                        raise

            except Exception as e:
                logger.error(f"Error processing submodules: {e}")
                raise
        else:
            logger.info("No .gitmodules file found - repository has no submodules")

        # Collect all download results
        completed = 0
        total = len(futures)

        for future in as_completed(futures):
            try:
                file = future.result()
                if file is not None:  # Only append successfully downloaded files
                    all_files.append(file)
                completed += 1

                if completed % 100 == 0:
                    logger.info(f"Downloaded {completed}/{total} files...")

            except Exception as e:
                logger.warning(f"Download failed: {e}")
                # Cancel remaining tasks and re-raise
                for f in futures:
                    f.cancel()
                raise

    logger.info(f"Total files downloaded: {len(all_files)}")
    return all_files


@blitzy_exponential_retry()
def process_submodule_parallel(
    parent_repo: Repository,
    submodule_path: str,
    info: Dict[str, str],
    commit_hash: str,
    branch_name: str,
    user_id: str,
    server: str,
    repo_id: Optional[str],
    rate_limiter: GitHubRateLimiter,
    executor: ThreadPoolExecutor
) -> List[Any]:
    """
    Process a submodule and return download tasks for its files.
    """
    futures = []

    try:
        # Get submodule commit hash
        submodule_commit = get_submodule_commit_sha_with_retry(
            repo=parent_repo,
            submodule_path=submodule_path,
            commit_hash=commit_hash
        )

        if not submodule_commit:
            logger.warning(f"Couldn't determine commit for submodule {submodule_path}")
            return futures

        # Parse submodule URL
        submodule_url = info['url']
        https_url = ssh_to_https_url(submodule_url)
        submodule_owner, submodule_repo_name, domain = extract_repo_info_from_url(https_url)

        if not submodule_owner or not submodule_repo_name:
            logger.error(f"Could not extract owner/repo from URL: {https_url}")
            return futures

        # Get access token and create GitHub client
        access_token, _ = _get_token_and_installation_id(server, user_id, repo_id)

        if domain and domain != 'github.com':
            g = Github(base_url=f"https://{domain}/api/v3", login_or_token=access_token)
        else:
            g = Github(access_token)

        # Get submodule repository
        submodule_repo = get_submodule_repo(g, submodule_owner, submodule_repo_name)

        # Fetch submodule metadata ONCE
        submodule_metadata = RepoMetadata.from_repo(submodule_repo, submodule_commit)

        # List all files in submodule with cached metadata
        submodule_files_list = get_all_files_in_repo(
            repo_name=submodule_repo_name,
            user_id=user_id,
            server=server,
            repo_id=repo_id,
            repo=submodule_repo,
            ref=submodule_commit,
            submodule_paths=submodule_metadata.submodule_paths
        )

        # Create download tasks for submodule files
        if not repo_id:
            raise ValueError("repo_id is required for submodule download tasks")
        for file_path, _ in submodule_files_list:
            task = DownloadTask(
                repo_name=submodule_repo_name,
                user_id=user_id,
                server=server,
                repo_id=repo_id,
                repo=submodule_repo,
                file_path=file_path,
                ref=submodule_commit,
                output_path=file_path,
                submodule_path=submodule_path,
                branch_name=branch_name,
                parent_repo_name=parent_repo.name,
                repo_metadata=submodule_metadata  # Pass cached metadata
            )
            future = executor.submit(download_file_with_rate_limit, task, rate_limiter)
            futures.append(future)

    except Exception as e:
        logger.error(f"Failed to process submodule {submodule_path}: {e}")
        raise

    return futures


def get_head_commit_hash(repo_name: str, user_id: str, server: str,
                         branch_name: str = "main", repo_id: Optional[str] = None,
                         git_project_repo_id: Optional[str] = None
                         ) -> str:
    """
    Get the head commit hash for a given branch in a git(github or azure currently) repository.

    :param git_project_repo_id: Has to be provided for azure support.
    """

    if git_project_repo_id:
        logger.info(f"Getting head commit hash for {repo_name} with git_project_repo_id {git_project_repo_id}")
        svc_type = _get_service_type(user_id=user_id, git_project_repo_id=git_project_repo_id)
    else:
        logger.info("No git_project_repo_id provided, using default github service type")
        svc_type = SvcType.GITHUB
    logger.info(f"Processing repository {repo_name} with service type: {svc_type.value}")

    if svc_type == SvcType.GITHUB:
        return _get_github_head_commit_hash(
            repo_name=repo_name,
            user_id=user_id,
            server=server,
            branch_name=branch_name,
            repo_id=repo_id
        )
    elif svc_type == SvcType.AZURE_DEVOPS and git_project_repo_id:
        return _get_azure_devops_head_commit_hash_by_repo_id(
            git_project_repo_id=git_project_repo_id,
            branch_name=branch_name
        )
    else:
        raise ValueError(f"Unsupported service type: {svc_type}")


def _get_github_head_commit_hash(repo_name: str, user_id: str, server: str,
                                 branch_name: str = "main", repo_id: Optional[str] = None) -> str:
    github_repo, _ = get_github_repo(repo_name=repo_name, user_id=user_id, server=server, create=False, repo_id=repo_id)

    if not isinstance(github_repo, Repository):
        raise ValueError("Expected a PyGithub Repository object")
    commit_hash = _get_head_commit_hash_github(
        branch_name=branch_name,
        repo=github_repo
    )
    logger.info(f"Head commit hash for branch {branch_name}: {commit_hash}")
    return commit_hash


@blitzy_exponential_retry()
def _get_head_commit_hash_github(branch_name: str, repo: Repository) -> str:
    """
    Get the head commit hash for a given branch in a GitHub repository.
    """
    try:
        branch = repo.get_branch(branch_name)
        return branch.commit.sha
    except GithubException as e:
        if e.status == 404:
            raise UnknownObjectException(404, f"Branch not found: {branch_name}")
        else:
            raise e


@blitzy_exponential_retry()
def get_git_commit(sha: str, repo: Repository):
    return repo.get_git_commit(sha)


@blitzy_exponential_retry()
def get_commit(sha: str, repo: Repository):
    return repo.get_commit(sha)


@blitzy_exponential_retry()
def compare_git_sha(base: str, head: str, repo: Repository):
    return repo.compare(base=base, head=head)


def get_changed_files_between_commits(
    repo_name: str,
    user_id: str,
    server: str,
    base_commit: str,
    head_commit: str,
    repo_id: Optional[str] = None,
    git_project_repo_id: Optional[str] = None,
) -> List[str]:
    """
    Get all files changed between two commits in a GitHub repository, including changes in submodules.

    Args:
        repo_name: Name of the repository
        user_id: User ID for authentication
        server: Server URL for authentication
        base_commit: Base commit hash to compare from
        head_commit: Head commit hash to compare to
        repo_id (Optional[str]): Optional project repository ID, used to identify the repository in question.
        git_project_repo_id (Optional[str]): Optional git project repository ID, used to switch handling to
        a unified endpoint(azure support)

    Returns:
        List of file paths that were changed between the two commits
    """
    # Get the service type for this repository
    if git_project_repo_id:
        logger.info(f"Getting head commit hash for {repo_name} with git_project_repo_id {git_project_repo_id}")
        svc_type = _get_service_type(user_id=user_id, git_project_repo_id=git_project_repo_id)
    else:
        logger.info("No git_project_repo_id provided, using default github service type")
        svc_type = SvcType.GITHUB
    logger.info(f"Processing repository {repo_name} with service type: {svc_type.value}")

    if svc_type == SvcType.GITHUB:
        return _get_changed_files_between_commits_github(
            repo_name=repo_name,
            user_id=user_id,
            server=server,
            base_commit=base_commit,
            head_commit=head_commit,
            repo_id=repo_id
        )
    elif svc_type == SvcType.AZURE_DEVOPS and git_project_repo_id:
        return _get_azure_devops_commit_diffs_by_repo_id(
            base_commit=base_commit,
            target_commit=head_commit,
            git_project_repo_id=git_project_repo_id
        )
    else:
        raise ValueError(f"Unsupported service type: {svc_type}")


def _get_changed_files_between_commits_github(
    repo_name: str,
    user_id: str,
    server: str,
    base_commit: str,
    head_commit: str,
    repo_id: Optional[str] = None
) -> List[str]:
    github_repo, _ = get_github_repo(repo_name=repo_name, user_id=user_id, server=server, create=False, repo_id=repo_id)

    try:
        if not isinstance(github_repo, Repository):
            raise ValueError("Expected a PyGithub Repository object")
        # Get comparison between the two commits
        comparison = compare_git_sha(base=base_commit, head=head_commit, repo=github_repo)

        # Initialize a set to store unique file paths
        changed_files_set = set()

        for commit in comparison.commits:
            full_commit = get_commit(sha=commit.sha, repo=github_repo)
            for file in full_commit.files:
                changed_files_set.add(file.filename)

        # Parse .gitmodules to identify submodules
        try:
            gitmodules_text = get_gitmodules_content(repo=github_repo, ref=head_commit)
            submodules = parse_gitmodules(gitmodules_text)
            logger.info(f"Found {len(submodules)} submodules in repository")

            # Check which submodules have changed
            changed_submodules = []
            for file_path in changed_files_set:
                for submodule_path in submodules:
                    if file_path == submodule_path:
                        changed_submodules.append(submodule_path)
                        break

            # Process each changed submodule
            for submodule_path in changed_submodules:
                info = submodules[submodule_path]
                logger.info(f"Processing changes in submodule: {submodule_path}")

                # Get the submodule commit hash at the base commit
                old_sha = get_submodule_commit_sha(github_repo, submodule_path, base_commit)

                # Get the submodule commit hash at the head commit
                new_sha = get_submodule_commit_sha(github_repo, submodule_path, head_commit)

                # Skip if we don't have both SHAs or they're the same
                if not new_sha or old_sha == new_sha:
                    logger.info(f"Skipping submodule {submodule_path} - no changes detected")
                    continue

                # Get the submodule repository
                submodule_url = info['url']
                https_url = ssh_to_https_url(submodule_url)
                submodule_owner, submodule_repo_name, domain = extract_repo_info_from_url(https_url)

                if not submodule_owner or not submodule_repo_name:
                    logger.error(f"Could not extract owner and repo name from URL: {https_url}")
                    continue

                # Get access to the submodule repository
                access_token, _ = _get_token_and_installation_id(server, user_id, repo_id)

                # Create appropriate Github object based on domain
                if domain and domain != 'github.com':
                    # GitHub Enterprise
                    g = Github(base_url=f"https://{domain}/api/v3", login_or_token=access_token)
                else:
                    # Public GitHub
                    g = Github(access_token)

                try:
                    submodule_repo = get_submodule_repo(g, submodule_owner, submodule_repo_name)

                    # Handle case where old_sha doesn't exist (newly added submodule)
                    if not old_sha:
                        logger.info(
                            f"Submodule {submodule_path} appears to be newly added - getting all files at {new_sha}")

                        # Get all files at the new commit hash
                        submodule_files = get_files_in_submodule(
                            submodule_repo=submodule_repo,
                            commit_hash=new_sha
                        )

                        # Add all files with the submodule path prefix
                        for file_path in submodule_files:
                            full_path = f"{submodule_path}/{file_path}"
                            changed_files_set.add(full_path)
                    else:
                        # Original comparison logic when we have both old and new SHA
                        submodule_comparison = compare_git_sha(base=old_sha, head=new_sha, repo=submodule_repo)

                        # Process each commit in the submodule to properly handle pagination
                        for submodule_commit in submodule_comparison.commits:
                            full_submodule_commit = get_commit(sha=submodule_commit.sha, repo=submodule_repo)

                            for submodule_file in full_submodule_commit.files:
                                # Add the file with the submodule path prefix
                                full_path = f"{submodule_path}/{submodule_file.filename}"
                                changed_files_set.add(full_path)

                    # Remove the submodule itself from the list since we're adding its contents
                    changed_files_set.discard(submodule_path)

                except GithubException as e:
                    logger.error(f"Error processing submodule {submodule_path}: {e}")

        except GithubException as e:
            logger.info(f"Could not find .gitmodules file: {e}")

        # Convert the set to a list
        changed_files = list(changed_files_set)

        logger.info(f"Found {len(changed_files)} changed files between commits {base_commit[:7]} and {head_commit[:7]}")
        return changed_files

    except GithubException as e:
        logger.error(f"Error comparing commits {base_commit} and {head_commit}: {e}")
        return []


@blitzy_exponential_retry()
def get_submodule_commit_sha(repo: GitRepo, submodule_path: str, commit_hash: str) -> Optional[str]:
    """
    Get the commit SHA that a submodule is pointing to at a specific commit in the parent repository.
    """
    if not isinstance(repo, Repository):
        raise ValueError("Expected a PyGithub Repository object")
    try:
        commit_obj = get_git_commit(sha=commit_hash, repo=repo)
        tree = repo.get_git_tree(commit_obj.tree.sha, recursive=True)

        for item in tree.tree:
            if item.path == submodule_path and item.type == "commit":
                logger.info(
                    f"Found submodule {submodule_path} with SHA {item.sha} in tree"
                )
                return item.sha
    except GithubException as e:
        logger.warning(f"Could not get submodule from Git tree: {e}")
        return None


def get_files_in_submodule(submodule_repo: GitRepo, commit_hash: str, git_project_repo_id: Optional[str] = None) -> List[str]:
    """Get all file paths in a submodule repository at a specific commit."""
    file_paths = []

    def get_contents_recursively(path=""):
        try:
            contents = get_contents_with_retry(repo=submodule_repo, path=path,
                                               ref=commit_hash, git_project_repo_id=git_project_repo_id)

            # Handle single file
            if not isinstance(contents, list):
                contents = [contents]

            for content in contents:
                if content.type == "dir":
                    get_contents_recursively(content.path)
                elif content.type == "file":
                    file_paths.append(content.path)
        except GithubException as e:
            logger.error(f"Error getting contents at {path}, commit {commit_hash}: {e}")

    get_contents_recursively()
    return file_paths


@blitzy_exponential_retry()
def _get_git_ref_with_retry(repo: Repository, ref: str) -> GitRef:
    """
    Get a Git reference (branch or tag) with retry logic.
    """
    try:
        return repo.get_git_ref(ref)
    except GithubException as e:
        if e.status == 404:
            raise UnknownObjectException(404, f"Git reference not found: {ref}")
        else:
            raise e


def create_github_commit(
    repo_name: str,
    repo_id: str,
    branch_name: str,
    base_branch: str,
    file_path: str,
    head_commit_hash: str,
    content="",
    create_new_branch=True,
    delete_file=False,
    is_new_repo=False,
    user_id: Optional[str] = None,
    server=None,
    commit_message=None,
    git_project_repo_id: Optional[str] = None,
) -> GitCommit | None | bool | AzureDevOpsCommit:
    if not user_id:
        logger.error("user_id must be provided to create a commit")
        raise ValueError("user_id is required for commit creation")
    if git_project_repo_id:
        svc_type = _get_service_type(user_id=user_id, git_project_repo_id=git_project_repo_id)
        logger.debug(f"Using service type {svc_type} from git_project_repo_id {git_project_repo_id}")
    else:
        logger.debug("Using fallback service type (GitHub)")
        svc_type = SvcType.GITHUB
    logger.info(f"Creating git commit for service type: {svc_type.value}")

    if svc_type == SvcType.GITHUB:
        return _create_github_commit(
            repo_name=repo_name,
            repo_id=repo_id,
            branch_name=branch_name,
            base_branch=base_branch,
            file_path=file_path,
            head_commit_hash=head_commit_hash,
            content=content,
            create_new_branch=create_new_branch,
            delete_file=delete_file,
            is_new_repo=is_new_repo,
            user_id=user_id,
            server=server,
            commit_message=commit_message
        )
    elif svc_type == SvcType.AZURE_DEVOPS and git_project_repo_id:
        return _create_azure_devops_commit_by_repo_id(
            branch_name=branch_name,
            base_branch=base_branch,
            file_path=file_path,
            content=content,
            create_new_branch=create_new_branch,
            delete_file=delete_file,
            is_new_repo=is_new_repo,
            commit_message=commit_message,
            git_project_repo_id=git_project_repo_id,
            head_commit_hash=head_commit_hash,
        )
    else:
        raise ValueError(f"Unsupported service type: {svc_type}")


def _create_github_commit(
    repo_name: str,
    repo_id: str,
    branch_name: str,
    base_branch: str,
    file_path: str,
    head_commit_hash: str,
    content="",
    create_new_branch=True,
    delete_file=False,
    is_new_repo=False,
    user_id: Optional[str] = None,
    server: Optional[str] = None,
    commit_message: Optional[str] = None,
) -> GitCommit | None | bool:
    """Create a commit for a file change with submodule support."""
    logger.info(f"Creating GitHub commit for file: {file_path}")

    if not user_id or not server:
        logger.error("user_id and server must be provided to create a commit")
        raise ValueError("user_id and server are required for commit creation")

    repo, _ = get_github_repo(
        repo_name=repo_name,
        user_id=user_id,
        server=server,
        create=create_new_branch,
        repo_id=repo_id
    )
    if not isinstance(repo, Repository):
        raise ValueError("Expected a PyGithub Repository object for GitHub create commit")

    # 1. Check if this file is in a submodule
    submodule_repo = None
    relative_path = None
    submodule_path = None

    # TODO: user_id will be gone
    if user_id and server and not is_new_repo:
        # This is the key check to properly detect submodules
        submodule_repo, relative_path, submodule_path = get_submodule_for_file(
            repo=repo,
            user_id=user_id,
            server=server,
            file_path=file_path,
            head_commit_hash=head_commit_hash,
        )

    # 2. If file is in a submodule, handle it with the submodule-specific logic
    if submodule_repo and submodule_path is not None and relative_path is not None:
        logger.info(
            f"File {file_path} is in submodule {submodule_path}, handling through submodule workflow"
        )
        return commit_to_submodule(
            main_repo=repo,
            branch_name=branch_name,
            base_branch=base_branch,
            submodule_repo=submodule_repo,
            submodule_path=submodule_path,
            relative_path=relative_path,
            content=content,
            create_new_branch=create_new_branch,
            delete_file=delete_file,
            commit_message=commit_message,
        )
    elif submodule_repo:
        logger.error("submodule_path or relative_path is None, cannot commit to submodule")
        raise ValueError("submodule_path and relative_path must not be None when committing to submodule")

    # 3. Regular (non-submodule) file handling continues below
    logger.info(
        f"File {file_path} is not in a submodule, proceeding with normal commit"
    )

    # Regular file handling (not in submodule)
    # Use repo's default branch if base_branch is not provided
    if not base_branch or is_new_repo:
        base_branch = repo.default_branch

    # If branch_name and base_branch are the same, ensure we're using the correct reference
    if branch_name == base_branch:
        raise Exception("Base branch cannot be same as target branch")

    # Handle base branch
    base_branch_ref = None
    try:
        base_branch_ref = _get_git_ref_with_retry(repo=repo, ref=f"heads/{base_branch}")
        logger.info(f"Base branch '{base_branch}' exists")
    except GithubException as e:
        if e.status == 404:
            if is_new_repo:
                logger.info(
                    f"Creating base branch '{base_branch}' with initial README.md"
                )
                _create_initial_commit_github(
                    repo=repo,
                    branch_name=base_branch,
                    commit_message="Initial commit with README.md",
                )
                base_branch_ref = _get_git_ref_with_retry(repo=repo, ref=f"heads/{base_branch}")
            else:
                logger.error(
                    f"Base branch '{base_branch}' does not exist and is_new_repo is False"
                )
                raise
        else:
            raise

    # Handle target branch
    branch_ref = None
    try:
        branch_ref = _get_git_ref_with_retry(repo=repo, ref=f"heads/{branch_name}")
        logger.info(f"Branch '{branch_name}' already exists")
    except GithubException as e:
        if e.status == 404:
            if create_new_branch:
                logger.info(f"Creating new branch '{branch_name}' from '{base_branch}'")
                branch_ref = _create_git_ref_with_retry_github(
                    branch_name=branch_name, base_branch_ref=base_branch_ref, repo=repo)
            else:
                logger.error(
                    f"Branch '{branch_name}' does not exist and create_new_branch is False"
                )
                raise
        else:
            raise

    # Perform the requested file operation
    if delete_file:
        return commit_delete_file(
            repo=repo,
            branch_name=branch_name,
            branch_ref=branch_ref,
            file_path=file_path,
            commit_message=commit_message,
        )
    else:
        return commit_create_or_update_file(
            repo=repo,
            branch_ref=branch_ref,
            file_path=file_path,
            content=content,
            commit_message=commit_message,
        )


@blitzy_exponential_retry()
def _create_initial_commit_github(
    repo: Repository,
    branch_name: str,
    commit_message: str = "Initial commit with README.md"
):
    """
    Create an initial commit with a README.md file in a new GitHub repository.
    """
    # Create a README.md file
    readme_content = "# Initial Commit\nThis is the initial commit with a README.md file."

    # Create or update the README.md file
    repo.create_file(
        path="README.md",
        message=commit_message,
        content=readme_content,
        branch=branch_name
    )


def get_submodule_for_file(
    repo: GitRepo,
    user_id: str,
    server: str,
    file_path: str,
    head_commit_hash: str,
    git_project_repo_id: Optional[str] = None
) -> Tuple[Optional[Repository], Optional[str], Optional[str]]:
    try:
        # Get .gitmodules file content
        try:
            gitmodules_text = get_gitmodules_content(
                repo=repo, ref=head_commit_hash, git_project_repo_id=git_project_repo_id)
        except GithubException as gm_error:
            logger.info(f"Could not find .gitmodules file: {gm_error}")
            return None, None, None

        # Parse .gitmodules file
        submodules = parse_gitmodules(gitmodules_text)

        # Find the submodule containing the file path
        matching_submodule_path = None
        for submodule_path in submodules:
            if file_path.startswith(submodule_path + "/"):
                matching_submodule_path = submodule_path
                break

        if matching_submodule_path:
            submodule_info = submodules[matching_submodule_path]
            submodule_url = submodule_info["url"]

            # Extract relative path within submodule
            relative_path = file_path[len(matching_submodule_path) + 1:]

            # Convert SSH URL to HTTPS if needed
            https_url = ssh_to_https_url(submodule_url)

            # Extract owner, repo name, and domain
            submodule_owner, submodule_repo_name, domain = extract_repo_info_from_url(
                https_url
            )

            if not submodule_owner or not submodule_repo_name:
                logger.error(
                    f"Could not extract owner and repo name from URL: {https_url}"
                )
                return None, None, None

            # Get the submodule repository
            if not isinstance(repo, Repository):
                logger.error("Provided repo is not a valid Repository object")
                return None, None, None

            # Get access token and installation ID
            access_token, _ = _get_token_and_installation_id(
                server, user_id, str(repo.id)
            )

            # Create appropriate Github object based on domain
            if domain and domain != "github.com":
                # GitHub Enterprise
                g = Github(
                    base_url=f"https://{domain}/api/v3", login_or_token=access_token
                )
            else:
                # Public GitHub
                g = Github(access_token)

            try:
                submodule_repo = get_submodule_repo(g, submodule_owner, submodule_repo_name)
                return submodule_repo, relative_path, matching_submodule_path
            except GithubException as repo_error:
                logger.error(f"Could not access submodule repository: {repo_error}")
                return None, relative_path, matching_submodule_path
        else:
            logger.error(f"File {file_path} does not match any submodule path")
            return None, None, None

    except Exception as submodule_error:
        logger.error(f"Error processing submodules: {submodule_error}")
        raise


@blitzy_exponential_retry()
def commit_delete_file(
    repo: GitRepo,
    branch_name: str,
    branch_ref: GitRef,
    file_path: str,
    commit_message=None,
):
    """Helper function to delete a file and create a commit."""
    try:
        # Get the file content first to get its sha
        file_content = get_contents_with_retry(repo=repo, path=file_path, ref=branch_name)

        # Delete the file
        message = commit_message or f"Delete file: {file_path}"
        if isinstance(repo, Repository) and isinstance(file_content, ContentFile):
            logger.info(f"Deleting file {file_path} from repository {repo.name} on branch {branch_name}")
            repo.delete_file(
                path=file_path, message=message, sha=file_content.sha, branch=branch_name
            )
        return True

    except GithubException as e:
        if e.status == 404:
            # File doesn't exist, nothing to delete
            logger.warning(f"File {file_path} does not exist, nothing to delete")
            return None
        else:
            # For other exceptions, re-raise
            logger.error(f"Error deleting GitHub file {file_path}: {str(e)}")
            raise


@blitzy_exponential_retry()
def commit_create_or_update_file(
    repo: Optional[Repository],
    branch_ref: GitRef,
    file_path: str,
    content: str,
    commit_message=None,
):
    """Helper function to create or update a file and create a commit."""
    try:
        # Create blob
        if not isinstance(repo, Repository):
            logger.error("Provided repo is not a valid Repository object")
            raise NotImplementedError("Creating or updating files is not implemented for non-PyGithub repositories")

        blob = repo.create_git_blob(content, "utf-8")

        # Get the latest commit on the branch
        base_tree = repo.get_git_tree(branch_ref.object.sha)

        # Create tree
        element = InputGitTreeElement(
            path=file_path, mode="100644", type="blob", sha=blob.sha
        )
        tree = repo.create_git_tree([element], base_tree)

        # Create commit
        parent = get_git_commit(sha=branch_ref.object.sha, repo=repo)
        message = commit_message or f"File: {file_path}"

        commit = repo.create_git_commit(message, tree, [parent])

        # Update branch reference to point to the new commit
        branch_ref.edit(commit.sha, force=True)

        return commit
    except Exception as e:
        logger.error(f"Error creating GitHub commit for {file_path}: {str(e)}")
        raise


@blitzy_exponential_retry()
def _create_git_ref_with_retry_github(
    branch_name: str,
    base_branch_ref: GitRef,
    repo: Repository
) -> GitRef:
    """
    Create a new Git reference (branch) with retry logic.
    """
    try:
        return repo.create_git_ref(
            ref=f"refs/heads/{branch_name}", sha=base_branch_ref.object.sha
        )
    except GithubException as e:
        if e.status == 422:
            logger.error(f"Branch '{branch_name}' already exists or invalid reference")
            raise
        elif e.status == 404:
            logger.error(f"Base branch reference not found: {base_branch_ref.ref}")
            raise
        else:
            logger.error(f"Failed to create Git reference for branch '{branch_name}': {e}")
            raise
    except Exception as e:
        logger.error(f"Unexpected error creating Git reference for branch '{branch_name}': {e}")
        raise


def commit_to_submodule(
    main_repo: GitRepo,
    branch_name: str,
    base_branch: str,
    submodule_repo: GitRepo,
    submodule_path: str,
    relative_path: str,
    content="",
    create_new_branch=True,
    delete_file=False,
    commit_message=None,
) -> GitCommit:
    """
    Handle committing changes to files within submodules.

    This will:
    1. Commit the change to the submodule
    2. Update the submodule reference in the main repository
    """
    logger.info(
        f"Handling change to file {relative_path} in submodule {submodule_path}"
    )

    # Get current submodule commit hash from main repo
    try:
        submodule_entry = get_contents_with_retry(repo=main_repo, path=submodule_path, ref=branch_name)
        if not isinstance(submodule_entry, ContentFile) or isinstance(submodule_entry, SimpleContentFile):
            logger.error(f"Submodule entry at {submodule_path} is not a file")
            raise ValueError(f"Submodule entry at {submodule_path} is not a file")
        current_submodule_commit = submodule_entry.sha
        logger.info(f"Current submodule commit is {current_submodule_commit}")
    except GithubException as e:
        logger.error(f"Error getting submodule reference: {e}")
        raise

    try:
        if not isinstance(submodule_repo, Repository):
            logger.error("Submodule repository is None, cannot commit to submodule")
            raise NotImplementedError("Committing to submodule is not implemented for non-PyGithub repositories")
        # Get submodule default branch
        submodule_default_branch = submodule_repo.default_branch

        # Try to get existing branch or create a new one
        try:
            submodule_branch_ref = _get_git_ref_with_retry(
                repo=submodule_repo, ref=f"heads/{branch_name}"
            )
            logger.info(f"Branch {branch_name} already exists in submodule")
        except GithubException as e:
            if e.status == 404 and create_new_branch:
                # Get the default branch ref
                submodule_default_ref = _get_git_ref_with_retry(
                    repo=submodule_repo, ref=f"heads/{submodule_default_branch}"
                )

                # Create new branch in submodule
                submodule_branch_ref = _create_git_ref_with_retry_github(
                    branch_name=branch_name,
                    base_branch_ref=submodule_default_ref,
                    repo=submodule_repo
                )
                logger.info(f"Created branch {branch_name} in submodule")
            else:
                raise

        # Commit the change to the submodule
        if delete_file:
            submodule_commit = commit_delete_file(
                repo=submodule_repo,
                branch_name=branch_name,
                branch_ref=submodule_branch_ref,
                file_path=relative_path,
                commit_message=commit_message,
            )
        else:
            submodule_commit = commit_create_or_update_file(
                repo=submodule_repo,
                branch_ref=submodule_branch_ref,
                file_path=relative_path,
                content=content,
                commit_message=commit_message,
            )

        if not submodule_commit:
            logger.error("Failed to create commit in submodule")
            raise Exception("Failed to create commit in submodule")

        # Now update the submodule reference in the main repo
        # First, ensure the branch exists in the main repo
        if not isinstance(main_repo, Repository):
            logger.error("Main repository is None, cannot commit to main repository")
            raise NotImplementedError("Committing to main repository is not implemented for non-PyGithub repositories")

        main_branch_ref = None
        try:
            main_branch_ref = _get_git_ref_with_retry(
                repo=main_repo, ref=f"heads/{branch_name}"
            )
            logger.info(f"Branch {branch_name} exists in main repo")
        except GithubException as e:
            if e.status == 404 and create_new_branch:
                # Get the base branch ref
                main_base_ref = _get_git_ref_with_retry(
                    repo=main_repo, ref=f"heads/{base_branch or main_repo.default_branch}"
                )

                # Create new branch
                main_branch_ref = _create_git_ref_with_retry_github(
                    branch_name=branch_name, base_branch_ref=main_base_ref, repo=main_repo
                )
                logger.info(f"Created branch {branch_name} in main repo")
            else:
                raise

        # Update the submodule in the main repo to point to the new commit
        main_tree = main_repo.get_git_tree(main_branch_ref.object.sha)

        # Create a new tree with the updated submodule
        # Fallback: if submodule_commit is a bool (from commit_delete_file), set sha to None
        submodule_commit_sha = getattr(submodule_commit, "sha", None)
        new_element = InputGitTreeElement(
            path=submodule_path,
            mode="160000",  # Special mode for submodules
            type="commit",
            sha=submodule_commit_sha,
        )
        new_tree = main_repo.create_git_tree([new_element], main_tree)

        # Create the commit in the main repo
        parent = get_git_commit(sha=main_branch_ref.object.sha, repo=main_repo)
        main_commit_message = commit_message or f"Update submodule {submodule_path} to include changes to {relative_path}"

        main_commit = main_repo.create_git_commit(
            message=main_commit_message, tree=new_tree, parents=[parent]
        )

        # Update the branch reference
        main_branch_ref.edit(main_commit.sha, force=True)

        logger.info(
            f"Updated submodule reference in main repo, commit {main_commit.sha}"
        )

        return main_commit

    except Exception as e:
        logger.error(f"Error updating submodule: {e}")
        raise


@blitzy_exponential_retry()
def create_all_pull_requests(
    repo_name: str,
    repo_id: str,
    head_branch: str,
    user_id: str,
    server: str,
    base_branch="",
    pr_title=None,
    pr_body=None,
    is_new_repo=False,
    git_project_repo_id: Optional[str] = None,
) -> List[Union[PullRequest, Dict]]:
    """
    Create pull requests for the specified branch and optionally for matching branches in submodules.
    Supports both GitHub and Azure DevOps repositories.

    Args:
        repo_name: The repository name
        repo_id: The repository ID
        head_branch: The branch containing changes to be merged
        user_id: User ID for authentication (needed for submodule access)
        server: Server URL for authentication (needed for submodule access)
        base_branch: The target branch for the PR (defaults to repo's default branch)
        pr_title: Custom PR title (defaults to "Autonomous changes created by Blitzy")
        pr_body: Custom PR body (defaults to standard message)
        is_new_repo: Whether this is a new repository
        git_project_repo_id: Repository ID for service type detection and Azure DevOps

    Returns:
        List of created/existing pull request objects (GitHub) or dictionaries (Azure DevOps)
    """
    # Determine service type
    if git_project_repo_id:
        logger.info(f"Using repository ID {git_project_repo_id} for repo type detection")
        svc_type = _get_service_type(user_id, git_project_repo_id)
        if svc_type == SvcType.AZURE_DEVOPS:
            logger.info("Detected Azure DevOps repository")
            # Use Azure DevOps implementation

            return create_all_pull_requests_azure_devops(
                git_project_repo_id=git_project_repo_id,
                head_branch=head_branch,
                base_branch=base_branch,
                pr_title=pr_title,
                pr_body=pr_body,
                is_new_repo=is_new_repo,
            )

    logger.info("Detected GitHub repository")
    # GitHub implementation (default)
    repo, _ = get_github_repo(
        repo_name=repo_name,
        user_id=user_id,
        server=server,
        create=is_new_repo,
        repo_id=repo_id,
        git_project_repo_id=git_project_repo_id
    )

    if not isinstance(repo, Repository):
        raise ValueError("Expected a PyGithub Repository object")
    if not base_branch or is_new_repo:
        base_branch = repo.default_branch

    created_prs = []

    # Create PR for main repository
    main_pr = create_single_pull_request(
        repo=repo,
        head_branch=head_branch,
        base_branch=base_branch,
        pr_title=pr_title or "Autonomous changes created by Blitzy",
        pr_body=pr_body or "This PR contains automated updates created by Blitzy"
    )
    created_prs.append(main_pr)

    # Check for submodules with matching branch
    try:
        # Get .gitmodules file content
        try:
            gitmodules_text = get_gitmodules_content(repo=repo, ref=base_branch)
        except GithubException as e:
            logger.warning(f"No .gitmodules file found assuming no submodules: {e}")
            return created_prs

        # Parse .gitmodules file
        submodules = parse_gitmodules(gitmodules_text)

        # For each submodule, check if the branch exists and create PR if it does
        for submodule_path, submodule_info in submodules.items():
            submodule_url = submodule_info['url']

            # Convert SSH URL to HTTPS if needed
            https_url = ssh_to_https_url(submodule_url)

            # Extract owner, repo name, and domain
            submodule_owner, submodule_repo_name, domain = extract_repo_info_from_url(https_url)

            if not submodule_owner or not submodule_repo_name:
                logger.error(f"Could not extract owner and repo name from URL: {https_url}")
                continue

            # Get the submodule repository
            if not isinstance(repo, Repository):
                logger.error("Provided repo is not a valid Repository object")
                raise NotImplementedError("Creating pull requests is not implemented for non-PyGithub repositories")

            access_token, _ = _get_token_and_installation_id(
                server, user_id, str(repo.id)
            )

            # Create appropriate Github object based on domain
            if domain and domain != 'github.com':
                # GitHub Enterprise
                g = Github(base_url=f"https://{domain}/api/v3", login_or_token=access_token)
            else:
                # Public GitHub
                g = Github(access_token)

            try:
                submodule_repo = get_submodule_repo(g, submodule_owner, submodule_repo_name)
                if not submodule_repo:
                    logger.info(f"Could not access submodule repository {submodule_owner}/{submodule_repo_name}")
                    continue

                # Check if the same branch exists in the submodule
                try:
                    submodule_repo.get_branch(head_branch)
                    logger.info(f"Found matching branch '{head_branch}' in submodule {submodule_path}")

                    # Create PR for the submodule
                    submodule_pr_title = f"Submodule update: {pr_title or 'Autonomous changes created by Blitzy'}"
                    submodule_pr_body = f"This PR is part of changes in the parent repository.\n\n{pr_body or 'This PR contains automated updates created by Blitzy'}"

                    if main_pr:
                        submodule_pr_body += f"\n\nRelated to parent repository PR: {main_pr.html_url}"

                    submodule_pr = create_single_pull_request(
                        repo=submodule_repo,
                        head_branch=head_branch,
                        base_branch=submodule_repo.default_branch,  # Use the submodule's default branch
                        pr_title=submodule_pr_title,
                        pr_body=submodule_pr_body
                    )
                    created_prs.append(submodule_pr)

                    new_body = main_pr.body + f"\n\nSubmodule PR created: {submodule_pr.html_url}"
                    main_pr.edit(body=new_body)

                except GithubException as branch_error:
                    if branch_error.status == 404:
                        logger.info(f"Branch '{head_branch}' does not exist in submodule {submodule_path}")
                    else:
                        logger.error(f"Error checking branch in submodule {submodule_path}: {branch_error}")

            except GithubException as repo_error:
                logger.error(f"Could not access submodule repository: {repo_error}")

    except Exception as e:
        logger.error(f"Error processing submodules for PR: {e}")
        raise

    return created_prs


@blitzy_exponential_retry()
def create_single_pull_request(
    repo: Union[Repository, str],
    head_branch: str,
    base_branch: str,
    pr_title: str,
    pr_body: str,
    user_id: Optional[str] = None,
    git_project_repo_id: Optional[str] = None,
) -> Optional[Union[PullRequest, Dict]]:
    """
    Create a single pull request in the specified repository.
    Supports both GitHub and Azure DevOps repositories.

    Args:
        repo: GitHub Repository object or repo name (for Azure DevOps)
        head_branch: The branch containing changes to be merged
        base_branch: The target branch for the PR
        pr_title: Title for the pull request
        pr_body: Description for the pull request
        user_id: User ID for service type detection (Azure DevOps)
        git_project_repo_id: Repository ID for service type detection and Azure DevOps

    Returns:
        GitHub PullRequest object or Azure DevOps pull request dictionary
    """
    # Determine service type for Azure DevOps
    if git_project_repo_id and user_id:
        logger.info(f"Using {git_project_repo_id} for repo type detection")
        svc_type = _get_service_type(user_id, git_project_repo_id)
        if svc_type == SvcType.AZURE_DEVOPS:
            logger.info("Detected Azure DevOps repository")
            # Use Azure DevOps implementation

            git_project_repo = _get_azure_devops_credentials_by_repo_id(
                git_project_repo_id
            )
            return create_single_pull_request_azure_devops(
                organization=git_project_repo.azure_org_name,
                project_id=git_project_repo.azure_project_id,
                repo_id=git_project_repo.repo_id,
                access_token=git_project_repo.access_token,
                head_branch=head_branch,
                base_branch=base_branch,
                pr_title=pr_title,
                pr_body=pr_body,
            )
    logger.info("Detected GitHub repository")
    # GitHub implementation (default)
    if not isinstance(repo, Repository):
        raise ValueError("For GitHub repositories, repo must be a Repository object")
    try:
        # Check if PR already exists
        existing_prs = repo.get_pulls(
            state="open", head=f"{repo.owner.login}:{head_branch}", base=base_branch
        )

        # Use existing PR if it exists
        for pr in existing_prs:
            logger.info(f"Using existing PR #{pr.number}: {pr.html_url}")
            return pr

        # Create new PR if none exists
        pr = repo.create_pull(
            title=pr_title, body=pr_body, head=head_branch, base=base_branch
        )

        logger.info(f"Created new PR #{pr.number}: {pr.html_url}")
        return pr

    except Exception as e:
        logger.error(f"Error handling pull request: {str(e)}")
        raise


@blitzy_exponential_retry()
def _handle_github_branch_logic(
        repo: Repository,
        branch_name: str,
        base_branch: str,
        create_new_branch: bool,
        delete_existing_branch: bool
) -> Optional[str]:  # Changed to Optional[str] to match what it actually returns
    """
    Internal logic for handling GitHub branch setup.

    Returns:
        str or None: The branch name if successful, or None for empty repos
    """
    # Check if repo is empty by attempting to get the default branch
    is_empty_repo = False
    try:
        repo.get_git_ref(f"heads/{base_branch}")
    except GithubException as e:
        if e.status == 409:
            logger.warning("Cannot create branch for an empty repository")
            is_empty_repo = True
        else:
            # If it's another GitHub exception, re-raise it
            raise

    if not is_empty_repo:
        branch_exists = True
        branch_ref = None  # Initialize to track the reference

        try:
            # Try to get the branch reference
            branch_ref = repo.get_git_ref(f"heads/{branch_name}")

            # If branch exists and delete_existing_branch is True, delete it
            if delete_existing_branch:
                logger.info(f'Deleting existing branch {branch_name}')
                branch_ref.delete()
                branch_exists = False
                branch_ref = None  # Clear the reference since we deleted it
            else:
                # Return branch name if we're not deleting it
                return branch_name

        except GithubException as e:
            # Only treat 404 as branch doesn't exist
            if e.status == 404:
                logger.warning(f'Branch {branch_name} does not exist')
                branch_exists = False
            else:
                # For any other GitHub exception, re-raise it
                raise

        # Create a new branch if needed (branch doesn't exist or was deleted)
        if (not branch_exists) and create_new_branch:
            # Get the base branch reference
            base_ref = repo.get_git_ref(f"heads/{base_branch}")

            # Create new branch from default branch
            branch_ref = repo.create_git_ref(
                ref=f"refs/heads/{branch_name}",
                sha=base_ref.object.sha
            )
            logger.info(f'Created new branch {branch_name}')
            return branch_name  # Return branch name instead of branch_ref
        elif not create_new_branch and not branch_exists:
            raise Exception(f"Branch {branch_name} does not exist and create_new_branch is False")

        # This handles the case where branch exists and we're not deleting it
        return branch_name
    else:
        return None


def setup_github_branch(
    repo_name: str,
    user_id: str,
    server: str,
    repo_id: str,
    branch_name: str,
    base_branch="",
    create_new_branch=True,
    delete_existing_branch=True,
    git_project_repo_id: Optional[str] = None
) -> str:
    repo, _ = get_github_repo(
        repo_name=repo_name,
        user_id=user_id,
        server=server,
        create=create_new_branch,
        repo_id=repo_id
    )

    if not base_branch:
        base_branch = repo.default_branch

    if git_project_repo_id and user_id:

        svc_type = _get_service_type(user_id, git_project_repo_id)

        if svc_type == SvcType.GITHUB:
            logger.info(
                f"Repo project {git_project_repo_id} is github repo type, will use github API to setup github branch"
            )

            # just to ensure error is not propogated further
            if not isinstance(repo, Repository):
                raise ValueError("Expected a PyGithub Repository object, but got AzureRepo")

            try:
                result = _handle_github_branch_logic(
                    repo=repo,
                    branch_name=branch_name,
                    base_branch=base_branch,
                    create_new_branch=create_new_branch,
                    delete_existing_branch=delete_existing_branch
                )
                return result if result else ""
            except Exception as e:
                logger.error(f"Error creating GitHub branch {branch_name}: {str(e)}")
                raise

        elif svc_type == SvcType.AZURE_DEVOPS and git_project_repo_id:
            logger.info(
                f"Repo project {git_project_repo_id} is github repo type, will use azure API to setup azure branch"
            )

            logger.info(
                f"Repo project {git_project_repo_id} is azure repo type, will use azure API to get repo info"
            )
            git_project_repo = _get_azure_devops_credentials_by_repo_id(git_project_repo_id)

            logger.info(
                f"Fetched github_project_repo details org_id: {git_project_repo.azure_org_id}, "
                f"azure_project_id: {git_project_repo.azure_project_id}, "
                f"repo_id: {git_project_repo.repo_id}"
            )

            try:
                result = _handle_azure_branch_logic(
                    git_project_repo.access_token,
                    git_project_repo.azure_org_name,
                    repo_id=git_project_repo.repo_id,
                    project=git_project_repo.azure_project_id,
                    branch_name=branch_name,
                    base_branch=base_branch,
                    create_new_branch=create_new_branch,
                    delete_existing_branch=delete_existing_branch
                )
                return result if result else ""

            except Exception as e:
                logger.error(f"Error creating Azure branch {branch_name}: {str(e)}")
                raise

        else:
            logger.error(f"Unknown service type {svc_type} for repo project {git_project_repo_id}")
            raise ValueError(f"Unsupported service type: {svc_type}")

    else:
        try:
            if isinstance(repo, AzureRepo):
                raise ValueError("Passed Azure object to pyGithub fallback logic")

            result = _handle_github_branch_logic(
                repo=repo,
                branch_name=branch_name,
                base_branch=base_branch,
                create_new_branch=create_new_branch,
                delete_existing_branch=delete_existing_branch
            )
            return result if result else ""
        except Exception as e:
            logger.error(f"Error creating GitHub branch {branch_name}: {str(e)}")
            raise


def get_repo_info_by_id(repo_id: str):
    """
    This function fetches repository metadata which is part of `github_project_repo` table.
    Sample response looks like this:
    {
        "installationType": "ORGANIZATION",
        "orgId": "16291886",
        "orgName": "blitzy-ai",
        "repoId": "1011128959",
        "repoName": "blitzy-utils-python",
        "svcType": null
    }
    :param repo_id: ID of the repository.
    :return: Github metadata.
    """
    with ServiceClient() as client:
        response = client.get("github", f"v1/github/repositories/{repo_id}")
        if response.status_code > 299:
            logger.warning(
                f"Failed to get repo metadata using github handler. "
                f"Status code: {response.status_code}, payload {response.text}"
            )
        response.raise_for_status()
        return response.json()


def add_auth_to_url(url: str, access_token: str, domain: Optional[str] = None) -> str:
    """
    Add authentication token to a Git HTTPS URL.

    Args:
        url: The HTTPS URL to add authentication to
        access_token: GitHub access token
        domain: GitHub domain (for GitHub Enterprise)

    Returns:
        URL with authentication added
    """
    # First convert SSH to HTTPS if needed
    url = ssh_to_https_url(url)

    # Add authentication to HTTPS URLs
    if url.startswith('https://'):
        if domain and domain != "github.com" and domain in url:
            return url.replace(f'https://{domain}/', f'https://x-access-token:{access_token}@{domain}/')
        elif 'github.com' in url:
            return url.replace('https://github.com/', f'https://x-access-token:{access_token}@github.com/')

    return url


def remove_auth_from_url(url: str) -> str:
    """Remove authentication token from a Git URL."""
    import re
    # Remove x-access-token:TOKEN@ pattern from URLs
    return re.sub(r'x-access-token:[^@]*@', '', url)


@blitzy_exponential_retry()
def clone_repository_with_auth(
    full_repo_name: str,
    access_token: str,
    clone_path: str,
    branch_name: str = "main",
    commit_hash: Optional[str] = None,
    domain: Optional[str] = None
) -> bool:
    """
    Clone a repository using git clone with authentication token.

    Args:
        full_repo_name: Repository name in format "owner/repo"
        access_token: GitHub access token for authentication
        clone_path: Local path where to clone the repository
        branch_name: Branch to clone
        commit_hash: Specific commit to checkout after cloning
        domain: GitHub domain (for GitHub Enterprise)

    Returns:
        True if successful, False otherwise
    """
    try:
        # Create parent directories if they don't exist
        os.makedirs(clone_path, exist_ok=True)

        # Build clone URL with authentication
        base_url = f"https://{domain}/{full_repo_name}.git" if domain and domain != "github.com" else f"https://github.com/{full_repo_name}.git"
        clone_url = add_auth_to_url(base_url, access_token, domain)

        # Set up environment for git command
        env = os.environ.copy()
        env['GIT_TERMINAL_PROMPT'] = '0'
        env['GIT_ASKPASS'] = 'echo'
        env['GCM_INTERACTIVE'] = 'never'
        env['GIT_CREDENTIAL_HELPER'] = ''

        # Build git clone command
        clone_cmd = [
            "git",
            "-c", "credential.helper=",
            "-c", "core.askpass=",
            "-c", "credential.interactive=never",
            "clone"
        ]

        # Add branch specification
        if branch_name and not commit_hash:
            clone_cmd.extend(["-b", branch_name])

        # Add repository URL and destination
        clone_cmd.extend([clone_url, clone_path])

        logger.info(f"Cloning repository: {full_repo_name} to {clone_path}")

        # Execute clone command
        result = subprocess.run(clone_cmd, capture_output=True, text=True, env=env, check=False)
        if result.returncode != 0:
            logger.error(f"Error cloning repository: {result.stderr}")
            return False

        logger.info(f"Repository cloned successfully to: {clone_path}")

        # Configure git to not use credential helpers for this repository
        subprocess.run(
            ["git", "-C", clone_path, "config", "--local", "credential.helper", ""],
            capture_output=True,
            check=False
        )

        # If specific commit hash is provided, checkout that commit
        if commit_hash:
            logger.info(f"Checking out specific commit: {commit_hash}")
            checkout_cmd = ["git", "-c", "credential.helper=", "-C", clone_path, "checkout", commit_hash]
            checkout_result = subprocess.run(checkout_cmd, capture_output=True, text=True, env=env, check=False)

            if checkout_result.returncode != 0:
                logger.error(f"Error checking out commit {commit_hash}: {checkout_result.stderr}")
                return False
            logger.info(f"Checked out commit: {commit_hash}")

        # Handle submodules if .gitmodules exists
        gitmodules_path = os.path.join(clone_path, ".gitmodules")
        original_urls = {}  # Initialize here to ensure it's always defined

        if os.path.exists(gitmodules_path):
            logger.info("Processing .gitmodules file...")

            # Set up URL rewriting for submodule operations
            base_url = f"https://{domain}/" if domain and domain != "github.com" else "https://github.com/"
            auth_url = add_auth_to_url(base_url, access_token, domain)

            # Configure URL rewriting
            url_rewrite_configs = [
                ["git", "config", "--global", f"url.{auth_url}.insteadOf", base_url],
                ["git", "config", "--global", f"url.{auth_url}.insteadOf", "**************:"],
                ["git", "config", "--global", f"url.{auth_url}.insteadOf", "ssh://**************/"]
            ]

            if domain and domain != "github.com":
                url_rewrite_configs.extend([
                    ["git", "config", "--global", f"url.{auth_url}.insteadOf", f"git@{domain}:"],
                    ["git", "config", "--global", f"url.{auth_url}.insteadOf", f"ssh://git@{domain}/"]
                ])

            # Apply URL rewriting configurations
            for config_cmd in url_rewrite_configs:
                subprocess.run(config_cmd, capture_output=True, text=True, check=False)

            # Process .gitmodules file to convert SSH URLs to HTTPS and add authentication
            import configparser
            config = configparser.ConfigParser()
            config.read(gitmodules_path)

            gitmodules_modified = False
            original_urls = {}  # Store original URLs for later restoration

            # Process each submodule section
            for section in config.sections():
                if section.startswith('submodule') and 'url' in config[section]:
                    original_url = config[section]['url']
                    original_urls[section] = original_url  # Store for later restoration

                    # Convert SSH to HTTPS and add authentication
                    https_url = ssh_to_https_url(original_url)
                    auth_url = add_auth_to_url(https_url, access_token, domain)

                    if auth_url != original_url:
                        config[section]['url'] = auth_url
                        gitmodules_modified = True

            # Write back the modified .gitmodules file if changes were made
            if gitmodules_modified:
                with open(gitmodules_path, 'w') as f:
                    config.write(f)
                logger.info("Updated .gitmodules file with authenticated HTTPS URLs")

            # Initialize and update submodules
            logger.info("Initializing and updating submodules...")

            # Initialize submodules
            init_cmd = ["git", "-c", "credential.helper=", "-C", clone_path, "submodule", "init"]
            init_result = subprocess.run(init_cmd, capture_output=True, text=True, env=env, check=False)

            if init_result.returncode != 0:
                logger.warning(f"Warning: Failed to initialize submodules: {init_result.stderr}")
            else:
                logger.info("Submodules initialized successfully")

            # Sync submodule configuration
            sync_cmd = ["git", "-c", "credential.helper=", "-C", clone_path, "submodule", "sync", "--recursive"]
            sync_result = subprocess.run(sync_cmd, capture_output=True, text=True, env=env, check=False)

            if sync_result.returncode != 0:
                logger.warning(f"Warning: Failed to sync submodules: {sync_result.stderr}")

            # Update submodules
            update_cmd = ["git", "-c", "credential.helper=", "-C", clone_path, "submodule", "update", "--recursive"]
            update_result = subprocess.run(update_cmd, capture_output=True, text=True, env=env, check=False)

            if update_result.returncode != 0:
                logger.warning(f"Warning: Some submodules may not have been updated: {update_result.stderr}")
            else:
                logger.info("Submodules updated successfully")

            # Restore original URLs in .gitmodules for security
            if gitmodules_modified and original_urls:
                logger.info("Restoring original URLs in .gitmodules...")
                for section, original_url in original_urls.items():
                    if section in config:
                        config[section]['url'] = original_url

                with open(gitmodules_path, 'w') as f:
                    config.write(f)
                logger.info("Restored original URLs in .gitmodules")

            # Clean up global URL rewriting configs
            for config_key in [f"url.{auth_url}.insteadOf"]:
                subprocess.run(["git", "config", "--global", "--unset-all",
                               config_key], capture_output=True, check=False)

        return True

    except Exception as e:
        logger.error(f"Exception during repository clone: {str(e)}")

        # Clean up any global config we might have set
        if 'auth_url' in locals():
            subprocess.run(
                ["git", "config", "--global", "--unset-all", f"url.{auth_url}.insteadOf"],
                capture_output=True,
                check=False
            )

        return False


@blitzy_exponential_retry()
def get_all_files_from_cloned_repo(
    clone_path: str
) -> List[BlitzyGitFile]:
    """
    Get all files from a cloned repository as BlitzyGitFile objects.

    Args:
        clone_path: Path where repository is cloned
        repo_name: Repository name
        branch_name: Branch name

    Returns:
        List of BlitzyGitFile objects
    """
    git_files = []
    file_count = 0

    # Walk through all files in the cloned repository
    for root, dirs, files in os.walk(clone_path):
        # Skip .git directory
        if '.git' in root.split(os.sep):
            continue

        for file in files:
            file_path = os.path.join(root, file)

            # Get relative path from clone_path
            relative_path = os.path.relpath(file_path, clone_path)

            # Convert Windows paths to Unix-style
            relative_path = relative_path.replace(os.sep, '/')

            try:
                # Read file content
                with open(file_path, 'r', encoding='utf-8') as f:
                    content = f.read()

                # Create BlitzyGitFile object
                git_file = BlitzyGitFile(
                    path=relative_path,
                    text=content
                )

                git_files.append(git_file)
                file_count += 1
                logger.info(f"Read file {file_count}: {relative_path}")

            except UnicodeDecodeError:
                # Try other encodings
                encodings = ['latin-1', 'cp1252', 'utf-16', 'ascii']
                decoded = False

                for encoding in encodings:
                    try:
                        with open(file_path, 'r', encoding=encoding) as f:
                            content = f.read()

                        git_file = BlitzyGitFile(
                            path=relative_path,
                            text=content
                        )

                        git_files.append(git_file)
                        file_count += 1
                        logger.info(f"Read file {file_count} (encoding: {encoding}): {relative_path}")
                        decoded = True
                        break

                    except UnicodeDecodeError:
                        continue

                if not decoded:
                    logger.warning(f"Could not decode file {relative_path}, skipping")

            except Exception as e:
                logger.error(f"Error reading file {relative_path}: {e}")

    logger.info(f"Total files read from cloned repository: {file_count}")
    return git_files


@blitzy_exponential_retry()
def download_repository_to_disk(
    repo_name: str,
    branch_name: str,
    user_id: str,
    server: str,
    commit_hash: str,
    repo_id: Optional[str] = None,
    git_project_repo_id: Optional[str] = None
) -> List[BlitzyGitFile]:
    """
    Download repository using git clone instead of GitHub API.

    Args:
        repo_name: Repository name
        branch_name: Branch to clone
        user_id: User ID for authentication
        server: Server URL for authentication
        commit_hash: Specific commit to checkout
        repo_id: Repository ID

    Returns:
        List of BlitzyGitFile objects
    """

    # Get the service type for this repository
    if git_project_repo_id:
        logger.info(f"Getting head commit hash for {repo_name} with git_project_repo_id {git_project_repo_id}")
        svc_type = _get_service_type(user_id=user_id, git_project_repo_id=git_project_repo_id)
    else:
        logger.info("No git_project_repo_id provided, using default github service type")
        svc_type = SvcType.GITHUB
    logger.info(f"Processing repository {repo_name} with service type: {svc_type.value}")

    if svc_type == SvcType.AZURE_DEVOPS:
        return download_all_git_files_to_disk(
            repo_name=repo_name,
            branch_name=branch_name,
            user_id=user_id,
            server=server,
            commit_hash=commit_hash,
            repo_id=repo_id,
            git_project_repo_id=git_project_repo_id
        )

    logger.info(f"Downloading {repo_name} from using git clone")

    # Get authentication token
    access_token, _ = _get_token_and_installation_id(
        server=server,
        user_id=user_id,
        repo_id=repo_id
    )

    if not access_token:
        raise Exception("Failed to get GitHub access token")

    # Extract owner and repo info
    if '/' in repo_name:
        full_repo_name = repo_name
    else:
        # Need to get full repo name with owner
        from blitzy_utils.github import get_repo_info_by_id
        repo_info = get_repo_info_by_id(repo_id)
        org_name = repo_info["orgName"]
        full_repo_name = f"{org_name}/{repo_name}"

    # Determine if this is GitHub Enterprise
    domain = None
    if '/' in full_repo_name and full_repo_name.count('/') > 1:
        # Extract domain from URL-like repo name
        parts = full_repo_name.split('/')
        if len(parts) > 2:
            domain = parts[0]
            full_repo_name = '/'.join(parts[1:])

    # Get disk path for cloning
    disk_path = get_cwd(
        repo_name=repo_name,
        branch_name=branch_name
    )

    # Clone the repository
    success = clone_repository_with_auth(
        full_repo_name=full_repo_name,
        access_token=access_token,
        clone_path=disk_path,
        branch_name=branch_name,
        commit_hash=commit_hash,
        domain=domain
    )

    if not success:
        raise Exception(f"Failed to clone repository {full_repo_name}")

    # Get all files from cloned repository
    git_files = get_all_files_from_cloned_repo(
        clone_path=disk_path
    )

    logger.info(f"Successfully downloaded {len(git_files)} files")
    return git_files
