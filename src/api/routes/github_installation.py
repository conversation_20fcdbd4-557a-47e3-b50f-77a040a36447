from blitzy_utils.service_client import ServiceClient
from flask import Blueprint
from flask_utils.decorators import flask_pydantic_response

from src.error.errors import check_service_error
from src.middleware.decorators import get_user_info
from src.service.github_installation_service import \
    get_active_installations_from_github_handler

github_bp = Blueprint("github", __name__, url_prefix="/v1/github")


@github_bp.route("", methods=["GET"])
@get_user_info()
@flask_pydantic_response
def get_github_installation_info(user_info):
    user_id = user_info["id"]
    github_installation = get_active_installations_from_github_handler(user_id)
    return github_installation, 200


@github_bp.route("/accounts", methods=["GET"])
@get_user_info()
@flask_pydantic_response
def get_github_accounts(user_info):
    """
    Get GitHub accounts/organizations for the authenticated user.

    This endpoint only handles GitHub organizations.
    """
    user_id = user_info["id"]
    # hard code to test Pratik
    user_id = "d87aed1d-934d-4248-aeec-4d1a1f97c2e6"
    response = get_only_github_organizations_from_github_handler(user_id)
    return response, 200


@github_bp.route("/accounts/<account_name>/repositories", methods=["GET"])
@get_user_info()
@flask_pydantic_response
def get_repositories_by_account_name(user_info, account_name):
    """
    Get repositories from a specific GitHub account/organization.

    This endpoint only handles GitHub repositories.
    """
    user_id = user_info["id"]
    user_id = "d87aed1d-934d-4248-aeec-4d1a1f97c2e6"
    response = get_only_github_repos_from_github_handler(user_id, account_name)
    return response, 200


@github_bp.route(
    "/accounts/<account_name>/repositories/<repo_id>/branches", methods=["GET"]
)
@get_user_info()
@flask_pydantic_response
def get_branches_by_repo_name(user_info, account_name, repo_id):
    """
    Get branches from a specific GitHub repository.

    This endpoint only handles GitHub repositories.
    """
    user_id = user_info["id"]
    response = get_branches_from_github_handler(user_id, account_name, repo_id)
    return response, 200


def get_organizations_from_github_handler(user_id: str):
    endpoint = f"/v1/users/{user_id}/organizations"
    with ServiceClient() as client:
        response = client.get("github", endpoint)
        check_service_error(response)
        return response.json()

def get_only_github_organizations_from_github_handler(user_id: str):
    endpoint = f"/v1/github/users/{user_id}/organizations"
    with ServiceClient() as client:
        response = client.get("github", endpoint)
        check_service_error(response)
        return response.json()

def get_only_github_repos_from_github_handler(user_id: str, account_name: str):
    with ServiceClient() as client:
        response = client.get(
            "github",
            f"/v1/github/users/{user_id}/organizations/{account_name}/repositories",
        )
        check_service_error(response)
        return response.json()


def get_repos_from_github_handler_with_scv_inference(
    user_id: str, account_name: str, repo_id_inference
):
    with ServiceClient() as client:
        response = client.get(
            "github",
            f"/v1/users/{user_id}/organizations/{account_name}/repositories/scv_inference/{repo_id_inference}",
        )
        check_service_error(response)
        return response.json()


def get_branches_from_github_handler(user_id: str, account_name: str, repo_id: str):
    with ServiceClient() as client:
        response = client.get(
            "github",
            f"/v1/users/{user_id}/organizations/{account_name}/repositories/{repo_id}/branches",
        )
        check_service_error(response)
        return response.json()

def get_azure_projects_from_handler(user_id: str, org_id: str):
    endpoint = f"/v1/azure/users/{user_id}/organizations/{org_id}/projects"
    with ServiceClient() as client:
        response = client.get("github", endpoint)
        check_service_error(response)
        return response.json()
